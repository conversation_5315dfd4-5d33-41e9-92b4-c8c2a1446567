#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德劳内三角剖分3D路径规划可视化工具
基于原有的visualize_3d_slices.py，适配三角网格系统
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import matplotlib.colors as mcolors

def load_triangular_grid_data(filename='triangular_grid_data.json'):
    """加载德劳内三角网格数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载数据文件: {filename}")
        return data
    except FileNotFoundError:
        print(f"数据文件 {filename} 不存在")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None

def visualize_triangular_layer(data, layer_index=0, show_triangles=True, show_obstacles=True):
    """可视化指定层的三角网格"""
    if not data or layer_index >= len(data['layers']):
        print(f"无效的层索引: {layer_index}")
        return
    
    layer = data['layers'][layer_index]
    map_size = data['map_size']
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 12))
    
    # 绘制三角形
    if show_triangles:
        for triangle in layer['triangles']:
            vertices = triangle['vertices']
            triangle_points = [(v['x'], v['y']) for v in vertices]
            
            # 根据节点类型选择颜色
            if triangle['node_type'] == 'Empty':
                color = 'lightblue'
                alpha = 0.3
            elif triangle['node_type'] == 'Blocked':
                color = 'red'
                alpha = 0.6
            else:  # Mixed
                color = 'yellow'
                alpha = 0.5
            
            # 根据LOD级别调整边框粗细
            linewidth = max(0.5, 2.0 - triangle['lod_level'] * 0.4)
            
            triangle_patch = Polygon(triangle_points, 
                                   facecolor=color, 
                                   edgecolor='black', 
                                   alpha=alpha,
                                   linewidth=linewidth)
            ax.add_patch(triangle_patch)
            
            # 显示三角形ID（仅对小数量）
            if len(layer['triangles']) < 100:
                center = triangle['center']
                ax.text(center['x'], center['y'], str(triangle['id']), 
                       ha='center', va='center', fontsize=8)
    
    # 绘制障碍物
    if show_obstacles:
        z_min, z_max = layer['z_min'], layer['z_max']
        for obstacle in data['obstacles']:
            if obstacle['type'] == 'Box':
                box = obstacle['Box']
                if box['z'] <= z_max and box['z'] + box['depth'] >= z_min:
                    rect = patches.Rectangle((box['x'], box['y']), 
                                           box['width'], box['height'],
                                           linewidth=2, edgecolor='darkred', 
                                           facecolor='red', alpha=0.7)
                    ax.add_patch(rect)
            
            elif obstacle['type'] == 'Cylinder':
                cyl = obstacle['Cylinder']
                if cyl['center']['z'] <= z_max and cyl['center']['z'] + cyl['height'] >= z_min:
                    circle = patches.Circle((cyl['center']['x'], cyl['center']['y']), 
                                          cyl['radius'],
                                          linewidth=2, edgecolor='darkred', 
                                          facecolor='red', alpha=0.7)
                    ax.add_patch(circle)
            
            elif obstacle['type'] == 'Prism':
                prism = obstacle['Prism']
                if prism['bottom'] <= z_max and prism['bottom'] + prism['height'] >= z_min:
                    vertices = [(v['x'], v['y']) for v in prism['vertices']]
                    polygon = Polygon(vertices, linewidth=2, edgecolor='darkred', 
                                    facecolor='red', alpha=0.7)
                    ax.add_patch(polygon)
    
    ax.set_xlim(0, map_size)
    ax.set_ylim(0, map_size)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title(f'德劳内三角网格 - 第{layer_index + 1}层 (Z: {layer["z_min"]:.1f} - {layer["z_max"]:.1f})\n'
                f'三角形数量: {layer["triangle_count"]}', fontsize=14)
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')
    
    # 添加图例
    legend_elements = [
        patches.Patch(color='lightblue', alpha=0.3, label='空旷三角形'),
        patches.Patch(color='red', alpha=0.6, label='被占据三角形'),
        patches.Patch(color='yellow', alpha=0.5, label='混合三角形'),
        patches.Patch(color='red', alpha=0.7, label='障碍物')
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.show()

def visualize_all_layers(data, max_layers=None):
    """可视化所有层的三角网格"""
    if not data:
        return
    
    layers = data['layers']
    if max_layers:
        layers = layers[:max_layers]
    
    n_layers = len(layers)
    cols = min(3, n_layers)
    rows = (n_layers + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 5*rows))
    if n_layers == 1:
        axes = [axes]
    elif rows == 1:
        axes = [axes]
    else:
        axes = axes.flatten()
    
    for i, layer in enumerate(layers):
        ax = axes[i] if n_layers > 1 else axes[0]
        
        # 绘制三角形
        for triangle in layer['triangles']:
            vertices = triangle['vertices']
            triangle_points = [(v['x'], v['y']) for v in vertices]
            
            if triangle['node_type'] == 'Empty':
                color = 'lightblue'
                alpha = 0.3
            elif triangle['node_type'] == 'Blocked':
                color = 'red'
                alpha = 0.6
            else:
                color = 'yellow'
                alpha = 0.5
            
            triangle_patch = Polygon(triangle_points, 
                                   facecolor=color, 
                                   edgecolor='black', 
                                   alpha=alpha,
                                   linewidth=0.5)
            ax.add_patch(triangle_patch)
        
        ax.set_xlim(0, data['map_size'])
        ax.set_ylim(0, data['map_size'])
        ax.set_aspect('equal')
        ax.set_title(f'层{i+1} (Z:{layer["z_min"]:.1f}-{layer["z_max"]:.1f})\n'
                    f'{layer["triangle_count"]}个三角形')
        ax.grid(True, alpha=0.3)
    
    # 隐藏多余的子图
    for i in range(n_layers, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.show()

def visualize_3d_triangular_grid(data, show_path=None, alpha=0.3):
    """3D可视化三角网格"""
    if not data:
        return
    
    fig = plt.figure(figsize=(15, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制三角形
    for layer in data['layers']:
        z_mid = (layer['z_min'] + layer['z_max']) / 2
        
        for triangle in layer['triangles']:
            if not triangle['is_active']:  # 只显示激活的三角形
                continue
                
            vertices = triangle['vertices']
            # 创建3D三角形顶点
            triangle_3d = [
                [vertices[0]['x'], vertices[0]['y'], z_mid],
                [vertices[1]['x'], vertices[1]['y'], z_mid],
                [vertices[2]['x'], vertices[2]['y'], z_mid]
            ]
            
            if triangle['node_type'] == 'Empty':
                color = 'lightblue'
            elif triangle['node_type'] == 'Blocked':
                color = 'red'
            else:
                color = 'yellow'
            
            # 添加3D三角形
            poly3d = [[triangle_3d]]
            ax.add_collection3d(Poly3DCollection(poly3d, 
                                               facecolors=color, 
                                               alpha=alpha,
                                               edgecolors='black',
                                               linewidths=0.5))
    
    # 绘制障碍物
    for obstacle in data['obstacles']:
        if obstacle['type'] == 'Box':
            box = obstacle['Box']
            # 绘制长方体的线框
            x, y, z = box['x'], box['y'], box['z']
            w, h, d = box['width'], box['height'], box['depth']
            
            # 定义长方体的8个顶点
            vertices = [
                [x, y, z], [x+w, y, z], [x+w, y+h, z], [x, y+h, z],  # 底面
                [x, y, z+d], [x+w, y, z+d], [x+w, y+h, z+d], [x, y+h, z+d]  # 顶面
            ]
            
            # 定义长方体的6个面
            faces = [
                [vertices[0], vertices[1], vertices[2], vertices[3]],  # 底面
                [vertices[4], vertices[5], vertices[6], vertices[7]],  # 顶面
                [vertices[0], vertices[1], vertices[5], vertices[4]],  # 前面
                [vertices[2], vertices[3], vertices[7], vertices[6]],  # 后面
                [vertices[1], vertices[2], vertices[6], vertices[5]],  # 右面
                [vertices[0], vertices[3], vertices[7], vertices[4]]   # 左面
            ]
            
            ax.add_collection3d(Poly3DCollection(faces, 
                                               facecolors='red', 
                                               alpha=0.7,
                                               edgecolors='darkred',
                                               linewidths=2))
    
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')
    ax.set_zlabel('Z坐标')
    ax.set_title('3D德劳内三角网格可视化')
    
    # 设置坐标轴范围
    ax.set_xlim(0, data['map_size'])
    ax.set_ylim(0, data['map_size'])
    ax.set_zlim(0, data['map_height'])
    
    plt.show()

def print_statistics(data):
    """打印统计信息"""
    if not data:
        return
    
    stats = data['statistics']
    print("\n=== 德劳内三角网格统计信息 ===")
    print(f"地图尺寸: {data['map_size']} x {data['map_size']} x {data['map_height']}")
    print(f"层厚度: {data['layer_thickness']}")
    print(f"总层数: {stats['total_layers']}")
    print(f"总三角形数量: {stats['total_triangles']}")
    print(f"激活三角形数量: {stats['active_triangles']}")
    print(f"空旷三角形数量: {stats['empty_triangles']}")
    print(f"被占据三角形数量: {stats['blocked_triangles']}")
    print(f"邻接边总数: {stats['total_adjacency_edges']}")
    print(f"LOD分布: {stats['lod_distribution']}")
    print(f"平均每层三角形数量: {stats['total_triangles'] / stats['total_layers']:.1f}")

def main():
    """主函数"""
    print("德劳内三角剖分3D路径规划可视化")
    print("=" * 50)
    
    # 加载数据
    data = load_triangular_grid_data()
    if not data:
        return
    
    # 打印统计信息
    print_statistics(data)
    
    # 可视化选项
    while True:
        print("\n可视化选项:")
        print("1. 查看单层三角网格")
        print("2. 查看所有层概览")
        print("3. 3D可视化")
        print("4. 退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == '1':
            layer_idx = int(input(f"请输入层索引 (0-{len(data['layers'])-1}): "))
            visualize_triangular_layer(data, layer_idx)
        
        elif choice == '2':
            max_layers = input("最大显示层数 (回车显示全部): ").strip()
            max_layers = int(max_layers) if max_layers else None
            visualize_all_layers(data, max_layers)
        
        elif choice == '3':
            visualize_3d_triangular_grid(data)
        
        elif choice == '4':
            break
        
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
