#!/usr/bin/env python3
"""
Python实现的自适应路径规划算法
包含多分辨率网格、A*路径规划、门优化等功能
"""

import numpy as np
import time
from typing import List, Tuple, Optional, Dict, Set, Any  # type: ignore
from dataclasses import dataclass
from enum import Enum
import heapq
import matplotlib.pyplot as plt
from matplotlib.figure import Figure  # type: ignore

# from matplotlib.axes import Axes
import numpy.typing as npt  # type: ignore


class NodeType(Enum):
    EMPTY = "Empty"
    OBSTACLE = "Obstacle"
    MIXED = "Mixed"


class ObstacleType(Enum):
    BOX = 1
    CYLINDER = 2
    PRISM = 3
    POINTSET = 4


class FaceDirection(Enum):
    X_POSITIVE = "XPositive"
    X_NEGATIVE = "XNegative"
    Y_POSITIVE = "YPositive"
    Y_NEGATIVE = "YNegative"
    Z_POSITIVE = "ZPositive"
    Z_NEGATIVE = "ZNegative"


@dataclass
class Point3D:
    x: float
    y: float
    z: float

    def distance_to(self, other: "Point3D") -> float:
        return np.sqrt(
            (self.x - other.x) ** 2 + (self.y - other.y) ** 2 + (self.z - other.z) ** 2
        )


@dataclass
class Box3D:
    x: float
    y: float
    z: float
    width: float
    height: float
    depth: float

    def contains_point(self, x: float, y: float, z: float) -> bool:
        return (
            self.x <= x <= self.x + self.width
            and self.y <= y <= self.y + self.height
            and self.z <= z <= self.z + self.depth
        )

    def center(self) -> Tuple[float, float, float]:
        return (
            self.x + self.width / 2,
            self.y + self.height / 2,
            self.z + self.depth / 2,
        )


@dataclass
class SharedFace3D:
    bounds: Box3D
    face_direction: FaceDirection


@dataclass
class GridCell3D:
    id: int
    bounds: Box3D
    node_type: NodeType
    level: int


@dataclass
class CorridorGate:
    cell_a: int
    cell_b: int
    shared_face: SharedFace3D


@dataclass
class Corridor:
    cells: List[int]
    gates: List[int]


@dataclass
class RefinedPath:
    points: List[Point3D]
    total_length: float
    smoothness: float


class VirtualGridNode:
    def __init__(self, x: int, y: int, z: int):
        self.x = x
        self.y = y
        self.z = z

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, VirtualGridNode):
            return False
        return self.x == other.x and self.y == other.y and self.z == other.z

    def __hash__(self):
        return hash((self.x, self.y, self.z))

    def get_neighbors_6(self) -> List["VirtualGridNode"]:
        return [
            VirtualGridNode(self.x + 1, self.y, self.z),
            VirtualGridNode(self.x - 1, self.y, self.z),
            VirtualGridNode(self.x, self.y + 1, self.z),
            VirtualGridNode(self.x, self.y - 1, self.z),
            VirtualGridNode(self.x, self.y, self.z + 1),
            VirtualGridNode(self.x, self.y, self.z - 1),
        ]

    def to_point3d(self) -> Point3D:
        return Point3D(self.x + 0.5, self.y + 0.5, self.z + 0.5)

    def distance_to(self, other: "VirtualGridNode") -> float:
        dx = self.x - other.x
        dy = self.y - other.y
        dz = self.z - other.z
        return np.sqrt(dx * dx + dy * dy + dz * dz)


class PathNode:
    def __init__(
        self, cell_id: int, g_cost: float, h_cost: float, parent: Optional[int] = None
    ):
        self.cell_id = cell_id
        self.g_cost = g_cost
        self.h_cost = h_cost
        self.parent = parent

    def f_cost(self) -> float:
        return self.g_cost + self.h_cost

    def __lt__(self, other: "PathNode") -> bool:
        return self.f_cost() < other.f_cost()


class VirtualPathNode:
    def __init__(
        self,
        node: VirtualGridNode,
        g_cost: float,
        h_cost: float,
        parent: Optional[VirtualGridNode] = None,
    ):
        self.node = node
        self.g_cost = g_cost
        self.h_cost = h_cost
        self.parent = parent

    def f_cost(self) -> float:
        return self.g_cost + self.h_cost

    def __lt__(self, other: "VirtualPathNode") -> bool:
        return self.f_cost() < other.f_cost()


class MultiResolutionGrid3D:
    def __init__(self, map_size: int, map_height: int):
        self.map_size = map_size
        self.map_height = map_height
        self.cells: List[GridCell3D] = []
        self.adjacency: Dict[int, List[Tuple[int, float]]] = {}
        self.corridor_gates: List[CorridorGate] = []
        self.corridors: List[Corridor] = []

        # 创建体素数据 - 改用字典存储稀疏障碍物，值为枚举类型
        self.obstacles: Dict[Tuple[int, int, int], ObstacleType] = {}
        self._obstacle_array: Optional[npt.NDArray[np.int32]] = None  # 用于快速查询

    def _update_obstacle_array(self):
        """将障碍物字典转换为Numpy数组以加速查询"""
        if self.obstacles:
            self._obstacle_array = np.array(list(self.obstacles.keys()), dtype=np.int32)
        else:
            self._obstacle_array = np.empty((0, 3), dtype=np.int32)

    def add_box_obstacle(
        self, x: float, y: float, z: float, w: float, h: float, d: float
    ):
        """添加长方体障碍物"""
        for i in range(int(x), min(int(x + w), self.map_size)):
            for j in range(int(y), min(int(y + h), self.map_size)):
                for k in range(int(z), min(int(z + d), self.map_height)):
                    self.obstacles[(i, j, k)] = ObstacleType.BOX

    def add_cylinder_obstacle(
        self, cx: float, cy: float, cz: float, radius: float, height: float
    ):
        """添加圆柱体障碍物"""
        for i in range(
            max(0, int(cx - radius)), min(int(cx + radius + 1), self.map_size)
        ):
            for j in range(
                max(0, int(cy - radius)), min(int(cy + radius + 1), self.map_size)
            ):
                for k in range(int(cz), min(int(cz + height), self.map_height)):
                    # 检查是否在圆柱体内
                    dx = i + 0.5 - cx
                    dy = j + 0.5 - cy
                    if dx * dx + dy * dy <= radius * radius:
                        self.obstacles[(i, j, k)] = ObstacleType.CYLINDER

    def add_prism_obstacle(
        self, vertices: List[Tuple[float, float]], bottom: float, height: float
    ):
        """添加棱柱障碍物"""
        if len(vertices) < 3:
            return

        # 找到边界框
        min_x = min(v[0] for v in vertices)
        max_x = max(v[0] for v in vertices)
        min_y = min(v[1] for v in vertices)
        max_y = max(v[1] for v in vertices)

        for i in range(max(0, int(min_x)), min(int(max_x + 1), self.map_size)):
            for j in range(max(0, int(min_y)), min(int(max_y + 1), self.map_size)):
                for k in range(int(bottom), min(int(bottom + height), self.map_height)):
                    # 检查点是否在多边形内（射线法）
                    if self._point_in_polygon(i + 0.5, j + 0.5, vertices):
                        self.obstacles[(i, j, k)] = ObstacleType.PRISM

    def add_pointset_obstacle(
        self, points: List[Tuple[float, float, float]], radius: float
    ):
        """添加3D点集障碍物"""
        for px, py, pz in points:
            for i in range(
                max(0, int(px - radius)), min(int(px + radius + 1), self.map_size)
            ):
                for j in range(
                    max(0, int(py - radius)), min(int(py + radius + 1), self.map_size)
                ):
                    for k in range(
                        max(0, int(pz - radius)),
                        min(int(pz + radius + 1), self.map_height),
                    ):
                        # 检查是否在球体内
                        dx = i + 0.5 - px
                        dy = j + 0.5 - py
                        dz = k + 0.5 - pz
                        if dx * dx + dy * dy + dz * dz <= radius * radius:
                            self.obstacles[(i, j, k)] = ObstacleType.POINTSET

    def _point_in_polygon(
        self, x: float, y: float, vertices: List[Tuple[float, float]]
    ) -> bool:
        """射线法判断点是否在多边形内"""
        n = len(vertices)
        inside = False

        p1x, p1y = vertices[0]
        for i in range(1, n + 1):
            p2x, p2y = vertices[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                            if p1x == p2x or x <= xinters:
                                inside = not inside
                        elif p1x == p2x:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def build_adaptive_grid(self):
        """构建3D自适应网格 - 严格按照Rust版本实现"""
        print("构建3D自适应网格...")

        self._update_obstacle_array()  # 在构建网格前更新Numpy数组

        self.cells.clear()
        self.adjacency.clear()
        self.corridor_gates.clear()
        self.corridors.clear()

        # 计算最大层级 - 与Rust版本一致
        theoretical_max_level = int(np.log2(self.map_size))
        min_grid_size = 2.0  # 硬编码最小网格大小
        max_level = min(
            theoretical_max_level, int(np.log2(self.map_size / min_grid_size))
        )

        print(f"理论最大层级: {theoretical_max_level}, 实际最大层级: {max_level}")

        # 从整个3D地图开始递归细化
        root_bounds = Box3D(
            0.0,
            0.0,
            0.0,
            float(self.map_size),
            float(self.map_size),
            float(self.map_height),
        )

        self.subdivide_region(root_bounds, 0, max_level)

        # 为每个单元分配ID
        for i, cell in enumerate(self.cells):
            cell.id = i

        print(f"3D网格构建完成，共 {len(self.cells)} 个单元")

    def subdivide_region(self, bounds: Box3D, level: int, max_level: int):
        """递归细分3D区域 - 确保网格边界对齐"""
        # 检查当前区域的类型
        node_type = self._determine_node_type_for_bounds(bounds)

        # 如果是纯空旷或纯障碍区域，或者已达到最大深度，或者已经是最小大小，则停止细分
        if (
            node_type != NodeType.MIXED
            or level >= max_level
            or bounds.width <= 1.0
            or bounds.height <= 1.0
            or bounds.depth <= 1.0
        ):
            final_node_type = (
                NodeType.OBSTACLE if node_type == NodeType.MIXED else node_type
            )
            cell = GridCell3D(len(self.cells), bounds, final_node_type, level)
            self.cells.append(cell)
            return

        # 如果是混合区域，则细分为8个子区域
        current_size = int(bounds.width)
        half_size = current_size // 2

        # 只有当当前大小是偶数时才能均匀细分
        if current_size % 2 != 0 or half_size == 0:
            cell = GridCell3D(len(self.cells), bounds, node_type, level)
            self.cells.append(cell)
            return

        x = int(bounds.x)
        y = int(bounds.y)
        z = int(bounds.z)

        # 8个子立方体 - 与Rust版本完全一致
        children = [
            Box3D(x, y, z, half_size, half_size, half_size),
            Box3D(x + half_size, y, z, half_size, half_size, half_size),
            Box3D(x, y + half_size, z, half_size, half_size, half_size),
            Box3D(x + half_size, y + half_size, z, half_size, half_size, half_size),
            Box3D(x, y, z + half_size, half_size, half_size, half_size),
            Box3D(x + half_size, y, z + half_size, half_size, half_size, half_size),
            Box3D(x, y + half_size, z + half_size, half_size, half_size, half_size),
            Box3D(
                x + half_size,
                y + half_size,
                z + half_size,
                half_size,
                half_size,
                half_size,
            ),
        ]

        # 递归处理每个子区域
        for child_bounds in children:
            self.subdivide_region(child_bounds, level + 1, max_level)

    def _determine_node_type(
        self, x: int, y: int, z: int, w: int, h: int, d: int
    ) -> NodeType:
        """确定节点类型"""
        obstacle_count = 0
        total_count = w * h * d
        
        if total_count == 0:
            return NodeType.EMPTY

        # 如果没有障碍物数组或没有障碍物，直接返回EMPTY
        if self._obstacle_array is None or self._obstacle_array.shape[0] == 0:
            return NodeType.EMPTY

        # 使用Numpy进行矢量化查询，比遍历所有障碍物或所有体素快得多
        obs = self._obstacle_array
        # 筛选出在x, y, z边界内的障碍物
        mask = (
            (obs[:, 0] >= x) & (obs[:, 0] < x + w) &
            (obs[:, 1] >= y) & (obs[:, 1] < y + h) &
            (obs[:, 2] >= z) & (obs[:, 2] < z + d)
        )
        obstacle_count = np.sum(mask)

        if obstacle_count == 0:
            return NodeType.EMPTY
        elif obstacle_count == total_count:
            # 只有当区域大小为1时，才可能完全匹配
            if total_count == 1:
                return NodeType.OBSTACLE
            # 对于更大的区域，精确判断是否完全填满仍然需要检查，但可以先简化处理
            # 实际应用中，一个大区域完全被障碍物填满的概率较低
            # 如果需要精确判断，仍需回退到循环检查，但当前优化已处理了大多数情况
            return NodeType.MIXED
        else:
            return NodeType.MIXED

    def _determine_node_type_for_bounds(self, bounds: Box3D) -> NodeType:
        """为Box3D边界确定节点类型"""
        return self._determine_node_type(
            int(bounds.x),
            int(bounds.y),
            int(bounds.z),
            int(bounds.width),
            int(bounds.height),
            int(bounds.depth),
        )

    def build_adjacency(self):
        """构建邻接关系 - 严格按照Rust版本实现"""
        print(f"构建邻接关系... ({len(self.cells)} 个网格单元)")

        self.adjacency.clear()

        # 预分配空间
        for i in range(len(self.cells)):
            self.adjacency[i] = []

        # 使用对称性优化：只检查i < j的情况
        for i in range(len(self.cells)):
            for j in range(i + 1, len(self.cells)):
                if self._are_adjacent_simple(i, j):
                    cost = self._calculate_edge_cost_by_id(i, j)

                    # 添加双向邻接关系
                    self.adjacency[i].append((j, cost))
                    self.adjacency[j].append((i, cost))

            # 进度显示
            if i % 1000 == 0 and i > 0:
                print(f"  已处理 {i}/{len(self.cells)} 个网格单元")

        print("邻接关系构建完成")

    def _are_adjacent_simple(self, cell_a_id: int, cell_b_id: int) -> bool:
        """简单检查两个网格单元是否相邻（按ID）"""
        cell_a = self.cells[cell_a_id]
        cell_b = self.cells[cell_b_id]

        # 跳过障碍物网格
        if (
            cell_a.node_type == NodeType.OBSTACLE
            or cell_b.node_type == NodeType.OBSTACLE
        ):
            return False

        return self._are_adjacent_fast(cell_a, cell_b)

    def _calculate_edge_cost_by_id(self, cell_a_id: int, cell_b_id: int) -> float:
        """按ID计算边的代价"""
        return self._calculate_edge_cost(self.cells[cell_a_id], self.cells[cell_b_id])

    def _get_spatial_key(self, bounds: Box3D) -> Tuple[int, int, int, int]:
        """获取网格的空间键"""
        # 使用网格的位置和大小作为键
        return (int(bounds.x), int(bounds.y), int(bounds.z), int(bounds.width))

    def _get_candidate_spatial_keys(
        self, bounds: Box3D
    ) -> List[Tuple[int, int, int, int]]:
        """获取可能相邻的空间键"""
        keys: List[Tuple[int, int, int, int]] = []
        x, y, z, w = int(bounds.x), int(bounds.y), int(bounds.z), int(bounds.width)

        # 检查相邻位置的网格
        for dx in [-w, 0, w]:
            for dy in [-w, 0, w]:
                for dz in [-w, 0, w]:
                    # 检查不同大小的网格
                    for size in [w // 2, w, w * 2]:
                        if size > 0:
                            keys.append((x + dx, y + dy, z + dz, size))

        return keys

    def _are_adjacent_fast(self, cell_a: GridCell3D, cell_b: GridCell3D) -> bool:
        """快速检查两个网格单元是否相邻"""
        a_bounds = cell_a.bounds
        b_bounds = cell_b.bounds

        # 快速边界检查 - 如果距离太远则不可能相邻
        max_size = max(
            a_bounds.width,
            a_bounds.height,
            a_bounds.depth,
            b_bounds.width,
            b_bounds.height,
            b_bounds.depth,
        )

        if (
            abs(a_bounds.x - b_bounds.x) > max_size
            or abs(a_bounds.y - b_bounds.y) > max_size
            or abs(a_bounds.z - b_bounds.z) > max_size
        ):
            return False

        # 检查是否有共享面（使用整数比较以提高速度）
        ax1, ay1, az1 = int(a_bounds.x), int(a_bounds.y), int(a_bounds.z)
        ax2, ay2, az2 = (
            ax1 + int(a_bounds.width),
            ay1 + int(a_bounds.height),
            az1 + int(a_bounds.depth),
        )
        bx1, by1, bz1 = int(b_bounds.x), int(b_bounds.y), int(b_bounds.z)
        bx2, by2, bz2 = (
            bx1 + int(b_bounds.width),
            by1 + int(b_bounds.height),
            bz1 + int(b_bounds.depth),
        )

        # X方向相邻
        if (
            (ax2 == bx1 or bx2 == ax1)
            and not (ay2 <= by1 or by2 <= ay1)
            and not (az2 <= bz1 or bz2 <= az1)
        ):
            return True

        # Y方向相邻
        if (
            (ay2 == by1 or by2 == ay1)
            and not (ax2 <= bx1 or bx2 <= ax1)
            and not (az2 <= bz1 or bz2 <= az1)
        ):
            return True

        # Z方向相邻
        if (
            (az2 == bz1 or bz2 == az1)
            and not (ax2 <= bx1 or bx2 <= ax1)
            and not (ay2 <= by1 or by2 <= ay1)
        ):
            return True

        return False

    def _are_adjacent(self, cell_a: GridCell3D, cell_b: GridCell3D) -> bool:
        """检查两个网格单元是否相邻（保留原方法作为备用）"""
        return self._are_adjacent_fast(cell_a, cell_b)

    def _calculate_edge_cost(self, cell_a: GridCell3D, cell_b: GridCell3D) -> float:
        """计算边的代价"""
        center_a = cell_a.bounds.center()
        center_b = cell_b.bounds.center()

        distance = np.sqrt(
            (center_a[0] - center_b[0]) ** 2
            + (center_a[1] - center_b[1]) ** 2
            + (center_a[2] - center_b[2]) ** 2
        )

        # 考虑网格大小差异的惩罚
        size_a = min(cell_a.bounds.width, cell_a.bounds.height, cell_a.bounds.depth)
        size_b = min(cell_b.bounds.width, cell_b.bounds.height, cell_b.bounds.depth)
        size_penalty = abs(size_a - size_b) * 0.1

        return distance + size_penalty

    def find_path_astar(
        self, start_point: Point3D, end_point: Point3D
    ) -> Optional[List[int]]:
        """使用A*算法寻找路径"""
        print(
            f"开始A*路径规划: ({start_point.x:.1f},{start_point.y:.1f},{start_point.z:.1f}) -> ({end_point.x:.1f},{end_point.y:.1f},{end_point.z:.1f})"
        )

        # 找到包含起点和终点的网格单元
        start_cell_id = self._find_cell_containing_point(start_point)
        end_cell_id = self._find_cell_containing_point(end_point)

        if start_cell_id is None or end_cell_id is None:
            print("无法找到包含起点或终点的网格单元")
            return None

        print(f"起点在网格单元 {start_cell_id}, 终点在网格单元 {end_cell_id}")

        # 检查起点和终点的邻接关系
        start_neighbors = len(self.adjacency.get(start_cell_id, []))
        end_neighbors = len(self.adjacency.get(end_cell_id, []))
        print(f"起点网格有 {start_neighbors} 个邻居，终点网格有 {end_neighbors} 个邻居")

        # 统计总的邻接关系数量
        total_edges = sum(len(neighbors) for neighbors in self.adjacency.values())
        print(
            f"总共有 {total_edges} 条边，平均每个网格 {total_edges / len(self.adjacency):.1f} 个邻居"
        )

        # A*算法
        open_set: List[PathNode] = []
        closed_set: Set[int] = set()
        came_from: Dict[int, int] = {}
        g_score = {start_cell_id: 0.0}

        start_h = self._heuristic_cost(start_cell_id, end_cell_id)
        heapq.heappush(open_set, PathNode(start_cell_id, 0.0, start_h))

        while open_set:
            current_node = heapq.heappop(open_set)
            current_id = current_node.cell_id

            if current_id == end_cell_id:
                # 重构路径
                path: List[int] = []
                current_path_id: int = current_id
                while current_path_id in came_from:
                    path.append(current_path_id)
                    current_path_id = int(came_from[current_path_id])
                path.append(start_cell_id)
                path.reverse()
                print(f"找到路径，长度: {len(path)}")
                return path

            closed_set.add(current_id)

            # 检查邻居
            if current_id in self.adjacency:
                for neighbor_id, edge_cost in self.adjacency[current_id]:
                    if neighbor_id in closed_set:
                        continue

                    tentative_g = current_node.g_cost + edge_cost

                    if neighbor_id not in g_score or tentative_g < g_score[neighbor_id]:
                        came_from[neighbor_id] = current_id
                        g_score[neighbor_id] = tentative_g
                        h_cost = self._heuristic_cost(neighbor_id, end_cell_id)
                        heapq.heappush(
                            open_set, PathNode(neighbor_id, tentative_g, h_cost)
                        )

        print("未找到路径")
        return None

    def _find_cell_containing_point(self, point: Point3D) -> Optional[int]:
        """找到包含指定点的最小网格单元"""
        best_cell_id = None
        best_cell_size = float("inf")
        candidates: List[Tuple[int, NodeType, float]] = []

        for cell in self.cells:
            if cell.bounds.contains_point(point.x, point.y, point.z):
                candidates.append(
                    (
                        cell.id,
                        cell.node_type,
                        min(cell.bounds.width, cell.bounds.height, cell.bounds.depth),
                    )
                )

                if cell.node_type != NodeType.OBSTACLE:
                    # 选择最小的非障碍网格单元
                    cell_size = min(
                        cell.bounds.width, cell.bounds.height, cell.bounds.depth
                    )
                    if cell_size < best_cell_size:
                        best_cell_size = cell_size
                        best_cell_id = cell.id

        print(
            f"  点({point.x:.1f},{point.y:.1f},{point.z:.1f})的候选网格: {len(candidates)}个"
        )
        for cell_id, node_type, size in candidates[:5]:  # 只显示前5个
            print(f"    网格{cell_id}: {node_type.value}, 大小{size}")

        if best_cell_id is None and candidates:
            print("警告: 点在障碍物区域内，尝试使用最近的空旷区域")
            # 如果点在障碍物内，尝试找最近的空旷网格
            return self._find_nearest_empty_cell(point)

        return best_cell_id

    def _find_nearest_empty_cell(self, point: Point3D) -> Optional[int]:
        """找到距离指定点最近的空旷网格单元"""
        min_distance = float("inf")
        nearest_cell_id = None

        for cell in self.cells:
            if cell.node_type == NodeType.EMPTY:
                center = cell.bounds.center()
                distance = (
                    (point.x - center[0]) ** 2
                    + (point.y - center[1]) ** 2
                    + (point.z - center[2]) ** 2
                ) ** 0.5
                if distance < min_distance:
                    min_distance = distance
                    nearest_cell_id = cell.id

        if nearest_cell_id is not None:
            print(f"  找到最近的空旷网格{nearest_cell_id}，距离{min_distance:.1f}")

        return nearest_cell_id

    def _heuristic_cost(self, cell_a_id: int, cell_b_id: int) -> float:
        """启发式代价函数"""
        center_a = self.cells[cell_a_id].bounds.center()
        center_b = self.cells[cell_b_id].bounds.center()

        return np.sqrt(
            (center_a[0] - center_b[0]) ** 2
            + (center_a[1] - center_b[1]) ** 2
            + (center_a[2] - center_b[2]) ** 2
        )

    def create_corridor_and_gates(self, path_cells: List[int]) -> Optional[int]:
        """为路径创建管道和门"""
        if len(path_cells) < 2:
            return None

        print(f"为路径创建管道和门，路径长度: {len(path_cells)}")

        # 创建门
        gates: List[int] = []
        for i in range(len(path_cells) - 1):
            cell_a_id = path_cells[i]
            cell_b_id = path_cells[i + 1]

            gate = self._create_gate_between_cells(cell_a_id, cell_b_id)
            if gate:
                gate_id = len(self.corridor_gates)
                self.corridor_gates.append(gate)
                gates.append(gate_id)

        # 创建管道
        corridor = Corridor(cells=path_cells, gates=gates)
        corridor_id = len(self.corridors)
        self.corridors.append(corridor)

        print(f"创建了 {len(gates)} 个门")
        return corridor_id

    def _create_gate_between_cells(
        self, cell_a_id: int, cell_b_id: int
    ) -> Optional[CorridorGate]:
        """在两个相邻网格单元之间创建门"""
        cell_a = self.cells[cell_a_id]
        cell_b = self.cells[cell_b_id]

        # 计算共享面
        shared_face = self._calculate_shared_face(cell_a.bounds, cell_b.bounds)
        if shared_face:
            return CorridorGate(cell_a_id, cell_b_id, shared_face)
        return None

    def _calculate_shared_face(
        self, bounds_a: Box3D, bounds_b: Box3D
    ) -> Optional[SharedFace3D]:
        """计算两个边界框的共享面"""
        # 检查X方向的共享面
        if abs(bounds_a.x + bounds_a.width - bounds_b.x) < 1e-6:
            # A的右面与B的左面相邻
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if y_end > y_start and z_end > z_start:
                face_bounds = Box3D(
                    bounds_a.x + bounds_a.width,
                    y_start,
                    z_start,
                    0,
                    y_end - y_start,
                    z_end - z_start,
                )
                return SharedFace3D(face_bounds, FaceDirection.X_POSITIVE)

        elif abs(bounds_b.x + bounds_b.width - bounds_a.x) < 1e-6:
            # B的右面与A的左面相邻
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if y_end > y_start and z_end > z_start:
                face_bounds = Box3D(
                    bounds_a.x, y_start, z_start, 0, y_end - y_start, z_end - z_start
                )
                return SharedFace3D(face_bounds, FaceDirection.X_NEGATIVE)

        # 检查Y方向的共享面
        if abs(bounds_a.y + bounds_a.height - bounds_b.y) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if x_end > x_start and z_end > z_start:
                face_bounds = Box3D(
                    x_start,
                    bounds_a.y + bounds_a.height,
                    z_start,
                    x_end - x_start,
                    0,
                    z_end - z_start,
                )
                return SharedFace3D(face_bounds, FaceDirection.Y_POSITIVE)

        elif abs(bounds_b.y + bounds_b.height - bounds_a.y) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if x_end > x_start and z_end > z_start:
                face_bounds = Box3D(
                    x_start, bounds_a.y, z_start, x_end - x_start, 0, z_end - z_start
                )
                return SharedFace3D(face_bounds, FaceDirection.Y_NEGATIVE)

        # 检查Z方向的共享面
        if abs(bounds_a.z + bounds_a.depth - bounds_b.z) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)

            if x_end > x_start and y_end > y_start:
                face_bounds = Box3D(
                    x_start,
                    y_start,
                    bounds_a.z + bounds_a.depth,
                    x_end - x_start,
                    y_end - y_start,
                    0,
                )
                return SharedFace3D(face_bounds, FaceDirection.Z_POSITIVE)

        elif abs(bounds_b.z + bounds_b.depth - bounds_a.z) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)

            if x_end > x_start and y_end > y_start:
                face_bounds = Box3D(
                    x_start, y_start, bounds_a.z, x_end - x_start, y_end - y_start, 0
                )
                return SharedFace3D(face_bounds, FaceDirection.Z_NEGATIVE)

        return None

    def refine_path_with_gates(
        self, corridor_id: int, start_point: Point3D, end_point: Point3D
    ) -> Optional[RefinedPath]:
        """使用门优化路径"""
        if corridor_id >= len(self.corridors):
            return None

        corridor = self.corridors[corridor_id]
        gate_ids = corridor.gates

        if not gate_ids:
            # 没有门，直接连接
            points = [start_point, end_point]
            total_length = self._calculate_path_length(points)
            return RefinedPath(points, total_length, 1.0)

        print(f"开始几何投影门优化，门数量: {len(gate_ids)}")

        # 使用几何投影方法计算每个门的最优通过点
        gate_points: List[Point3D] = []
        current_point = start_point

        for i, gate_id in enumerate(gate_ids):
            if gate_id < len(self.corridor_gates):
                gate = self.corridor_gates[gate_id]
                optimal_point = self._find_closest_point_in_gate(current_point, gate)
                print(
                    f"门{i}: 当前点({current_point.x:.1f},{current_point.y:.1f},{current_point.z:.1f}) -> 最优点({optimal_point.x:.1f},{optimal_point.y:.1f},{optimal_point.z:.1f})"
                )
                gate_points.append(optimal_point)
                current_point = optimal_point

        # 构建初始路径
        initial_points = [start_point] + gate_points + [end_point]
        print(f"初始路径点数: {len(initial_points)}")

        # 应用跨点直连优化
        optimized_points = self._optimize_path_with_line_of_sight(
            initial_points, gate_ids
        )

        # 计算路径长度和平滑度
        total_length = self._calculate_path_length(optimized_points)
        smoothness = self._calculate_path_smoothness(optimized_points)

        print(
            f"跨点优化完成，路径点数: {len(initial_points)} -> {len(optimized_points)}, 总长度: {total_length:.2f}"
        )

        return RefinedPath(optimized_points, total_length, smoothness)

    def _find_closest_point_in_gate(
        self, current_point: Point3D, gate: CorridorGate
    ) -> Point3D:
        """基于几何投影找到门内距离当前点最近的点"""
        bounds = gate.shared_face.bounds
        face_direction = gate.shared_face.face_direction

        if face_direction in [FaceDirection.X_POSITIVE, FaceDirection.X_NEGATIVE]:
            # X轴朝向门
            projected_y = current_point.y
            projected_z = current_point.z

            # 检查投影点是否在门内
            if (
                bounds.y <= projected_y <= bounds.y + bounds.height
                and bounds.z <= projected_z <= bounds.z + bounds.depth
            ):
                gate_x = bounds.x + bounds.width / 2.0
                return Point3D(gate_x, projected_y, projected_z)
            else:
                # 夹紧到边界
                clamped_y = max(bounds.y, min(projected_y, bounds.y + bounds.height))
                clamped_z = max(bounds.z, min(projected_z, bounds.z + bounds.depth))
                gate_x = bounds.x + bounds.width / 2.0
                return Point3D(gate_x, clamped_y, clamped_z)

        elif face_direction in [FaceDirection.Y_POSITIVE, FaceDirection.Y_NEGATIVE]:
            # Y轴朝向门
            projected_x = current_point.x
            projected_z = current_point.z

            if (
                bounds.x <= projected_x <= bounds.x + bounds.width
                and bounds.z <= projected_z <= bounds.z + bounds.depth
            ):
                gate_y = bounds.y + bounds.height / 2.0
                return Point3D(projected_x, gate_y, projected_z)
            else:
                clamped_x = max(bounds.x, min(projected_x, bounds.x + bounds.width))
                clamped_z = max(bounds.z, min(projected_z, bounds.z + bounds.depth))
                gate_y = bounds.y + bounds.height / 2.0
                return Point3D(clamped_x, gate_y, clamped_z)

        else:  # Z轴朝向门
            projected_x = current_point.x
            projected_y = current_point.y

            if (
                bounds.x <= projected_x <= bounds.x + bounds.width
                and bounds.y <= projected_y <= bounds.y + bounds.height
            ):
                gate_z = bounds.z + bounds.depth / 2.0
                return Point3D(projected_x, projected_y, gate_z)
            else:
                clamped_x = max(bounds.x, min(projected_x, bounds.x + bounds.width))
                clamped_y = max(bounds.y, min(projected_y, bounds.y + bounds.height))
                gate_z = bounds.z + bounds.depth / 2.0
                return Point3D(clamped_x, clamped_y, gate_z)

    def _optimize_path_with_line_of_sight(
        self, initial_points: List[Point3D], gate_ids: List[int]
    ) -> List[Point3D]:
        """跨点直连优化"""
        if len(initial_points) <= 2:
            return initial_points

        optimized_path = [initial_points[0]]  # 起点
        current_index = 0

        while current_index < len(initial_points) - 1:
            current_point = initial_points[current_index]
            furthest_reachable_index = current_index + 1

            # 尝试跳过尽可能多的中间点
            for target_index in range(current_index + 2, len(initial_points)):
                target_point = initial_points[target_index]

                if self._can_reach_directly_through_gates(
                    current_point, target_point, current_index, target_index, gate_ids
                ):
                    furthest_reachable_index = target_index
                    print(f"  可以跳过: 从点{current_index} 直接到点{target_index}")
                else:
                    break

            # 移动到最远可达的点
            if furthest_reachable_index < len(initial_points) - 1:
                optimized_path.append(initial_points[furthest_reachable_index])

            current_index = furthest_reachable_index

        # 添加终点
        optimized_path.append(initial_points[-1])
        return optimized_path

    def _can_reach_directly_through_gates(
        self,
        start_point: Point3D,
        end_point: Point3D,
        start_index: int,
        end_index: int,
        gate_ids: List[int],
    ) -> bool:
        """检查是否能直接穿过所有中间门"""
        # 检查直线路径是否穿过所有必要的门
        for gate_index in range(start_index + 1, end_index):
            if gate_index - 1 < len(gate_ids):
                gate_id = gate_ids[gate_index - 1]  # 门的索引比点的索引少1
                if gate_id < len(self.corridor_gates):
                    gate = self.corridor_gates[gate_id]
                    if not self._line_passes_through_gate_bounds(
                        start_point, end_point, gate
                    ):
                        return False
        return True

    def _line_passes_through_gate_bounds(
        self, start: Point3D, end: Point3D, gate: CorridorGate
    ) -> bool:
        """检查直线是否穿过门的边界"""
        bounds = gate.shared_face.bounds
        face_direction = gate.shared_face.face_direction

        # 计算直线与门所在平面的交点
        if face_direction in [FaceDirection.X_POSITIVE, FaceDirection.X_NEGATIVE]:
            # 门在X平面上
            gate_x = bounds.x + bounds.width / 2.0
            intersection = self._line_plane_intersection_x(start, end, gate_x)
            if intersection:
                # 检查交点是否在门的YZ边界内
                return (
                    bounds.y <= intersection.y <= bounds.y + bounds.height
                    and bounds.z <= intersection.z <= bounds.z + bounds.depth
                )
            return False

        elif face_direction in [FaceDirection.Y_POSITIVE, FaceDirection.Y_NEGATIVE]:
            # 门在Y平面上
            gate_y = bounds.y + bounds.height / 2.0
            intersection = self._line_plane_intersection_y(start, end, gate_y)
            if intersection:
                # 检查交点是否在门的XZ边界内
                return (
                    bounds.x <= intersection.x <= bounds.x + bounds.width
                    and bounds.z <= intersection.z <= bounds.z + bounds.depth
                )
            return False

        else:  # Z方向
            # 门在Z平面上
            gate_z = bounds.z + bounds.depth / 2.0
            intersection = self._line_plane_intersection_z(start, end, gate_z)
            if intersection:
                # 检查交点是否在门的XY边界内
                return (
                    bounds.x <= intersection.x <= bounds.x + bounds.width
                    and bounds.y <= intersection.y <= bounds.y + bounds.height
                )
            return False

    def _line_plane_intersection_x(
        self, start: Point3D, end: Point3D, plane_x: float
    ) -> Optional[Point3D]:
        """计算直线与X平面的交点"""
        dx = end.x - start.x
        if abs(dx) < 1e-6:
            return None  # 直线平行于平面

        t = (plane_x - start.x) / dx
        if 0.0 <= t <= 1.0:
            return Point3D(
                plane_x,
                start.y + t * (end.y - start.y),
                start.z + t * (end.z - start.z),
            )
        return None

    def _line_plane_intersection_y(
        self, start: Point3D, end: Point3D, plane_y: float
    ) -> Optional[Point3D]:
        """计算直线与Y平面的交点"""
        dy = end.y - start.y
        if abs(dy) < 1e-6:
            return None  # 直线平行于平面

        t = (plane_y - start.y) / dy
        if 0.0 <= t <= 1.0:
            return Point3D(
                start.x + t * (end.x - start.x),
                plane_y,
                start.z + t * (end.z - start.z),
            )
        return None

    def _line_plane_intersection_z(
        self, start: Point3D, end: Point3D, plane_z: float
    ) -> Optional[Point3D]:
        """计算直线与Z平面的交点"""
        dz = end.z - start.z
        if abs(dz) < 1e-6:
            return None  # 直线平行于平面

        t = (plane_z - start.z) / dz
        if 0.0 <= t <= 1.0:
            return Point3D(
                start.x + t * (end.x - start.x),
                start.y + t * (end.y - start.y),
                plane_z,
            )
        return None

    def _calculate_path_length(self, points: List[Point3D]) -> float:
        """计算路径长度"""
        if len(points) < 2:
            return 0.0

        total_length = 0.0
        for i in range(len(points) - 1):
            total_length += points[i].distance_to(points[i + 1])
        return total_length

    def _calculate_path_smoothness(self, points: List[Point3D]) -> float:
        """计算路径平滑度"""
        if len(points) < 3:
            return 1.0

        total_angle_change = 0.0
        for i in range(1, len(points) - 1):
            # 计算角度变化
            v1 = np.array(
                [
                    points[i].x - points[i - 1].x,
                    points[i].y - points[i - 1].y,
                    points[i].z - points[i - 1].z,
                ]
            )
            v2 = np.array(
                [
                    points[i + 1].x - points[i].x,
                    points[i + 1].y - points[i].y,
                    points[i + 1].z - points[i].z,
                ]
            )

            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)

            if norm1 > 1e-6 and norm2 > 1e-6:
                cos_angle = np.dot(v1, v2) / (norm1 * norm2)
                cos_angle = np.clip(cos_angle, -1.0, 1.0)
                angle = np.arccos(cos_angle)
                total_angle_change += angle

        # 平滑度：角度变化越小越平滑
        return 1.0 / (1.0 + total_angle_change)


def create_grid_subdivision_slices(grid: MultiResolutionGrid3D):
    """1. 网格细分切片图 - 显示不同切片的网格细分情况"""
    print("创建网格细分切片图...")

    # 选择几个关键Z层
    z_levels = [2, 8, 16, 24, 30]
    z_levels = [z for z in z_levels if z < grid.map_height]

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))  # type: ignore
    axes = axes.flatten()

    for i, z_level in enumerate(z_levels[:6]):
        if i >= len(axes):
            break

        ax = axes[i]

        # 绘制障碍物（黑色）
        for x in range(grid.map_size):
            for y in range(grid.map_size):
                if z_level < grid.map_height and (x, y, z_level) in grid.obstacles:
                    ax.add_patch(
                        plt.Rectangle((x, y), 1, 1, facecolor="black", alpha=0.8)  # type: ignore
                    )

        # 绘制网格边界 - 只显示红色和绿色边框
        for cell in grid.cells:
            if cell.bounds.z <= z_level < cell.bounds.z + cell.bounds.depth:
                if cell.node_type == NodeType.OBSTACLE:
                    # 禁飞区 - 红色边框
                    rect = plt.Rectangle(  # type: ignore
                        (cell.bounds.x, cell.bounds.y),
                        cell.bounds.width,
                        cell.bounds.height,
                        fill=False,
                        edgecolor="red",
                        alpha=0.8,
                        linewidth=1.5,
                    )
                    ax.add_patch(rect)
                elif cell.node_type == NodeType.EMPTY:
                    # 可飞行区域 - 绿色边框
                    rect = plt.Rectangle(  # type: ignore
                        (cell.bounds.x, cell.bounds.y),
                        cell.bounds.width,
                        cell.bounds.height,
                        fill=False,
                        edgecolor="green",
                        alpha=0.6,
                        linewidth=0.8,
                    )
                    ax.add_patch(rect)
                # 不显示混合区域的边框

        ax.set_xlim(0, grid.map_size)
        ax.set_ylim(0, grid.map_size)
        ax.set_aspect("equal")
        ax.set_title(f"网格细分 Z={z_level}", fontsize=12)
        ax.grid(True, alpha=0.2)

        # 添加图例
        if i == 0:
            from matplotlib.lines import Line2D

            legend_elements = [
                Line2D([0], [0], color="black", lw=4, label="障碍物"),
                Line2D([0], [0], color="red", lw=2, label="禁飞区"),
                Line2D([0], [0], color="green", lw=2, label="可飞行区域"),
                Line2D([0], [0], color="gray", lw=1, label="混合区域"),
            ]
            ax.legend(
                handles=legend_elements,
                bbox_to_anchor=(1.05, 1),
                loc="upper left",
                fontsize=8,
            )

    # 隐藏多余的子图
    for i in range(len(z_levels), len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()
    plt.savefig("grid_subdivision_slices.png", dpi=150, bbox_inches="tight")  # type: ignore
    print("网格细分切片图保存为 grid_subdivision_slices.png")
    return fig


def create_path_views(
    grid: MultiResolutionGrid3D,
    coarse_path: List[int],
    refined_path: Optional[RefinedPath],
    start_point: Point3D,
    end_point: Point3D,
):
    """2. 路径线路图 - 显示障碍物和路径的三个方向视图"""
    print("创建路径线路图...")

    fig, axes = plt.subplots(1, 3, figsize=(18, 6))  # type: ignore

    # XY视图 (俯视图)
    ax_xy = axes[0]
    # 绘制障碍物投影
    for x in range(grid.map_size):
        for y in range(grid.map_size):
            has_obstacle = any(
                (x, y, z) in grid.obstacles for z in range(grid.map_height)
            )
            if has_obstacle:
                ax_xy.add_patch(plt.Rectangle((x, y), 1, 1, facecolor="red", alpha=0.6))  # type: ignore

    # 绘制粗路径
    if coarse_path:
        coarse_points: List[Tuple[float, float]] = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append((center[0], center[1]))
        if len(coarse_points) > 1:
            xs, ys = zip(*coarse_points)
            ax_xy.plot(xs, ys, "orange", linewidth=4, alpha=0.8, label="粗路径")
            ax_xy.scatter(xs, ys, c="orange", s=60, marker="o", alpha=0.9)

    # 绘制细化路径
    if refined_path and len(refined_path.points) > 1:
        xs = [p.x for p in refined_path.points]
        ys = [p.y for p in refined_path.points]
        ax_xy.plot(xs, ys, "blue", linewidth=2, alpha=0.9, label="细化路径")

    # 起点终点
    ax_xy.scatter(
        [start_point.x], [start_point.y], c="green", s=200, marker="s", label="起点"
    )
    ax_xy.scatter(
        [end_point.x], [end_point.y], c="red", s=200, marker="^", label="终点"
    )

    ax_xy.set_xlim(0, grid.map_size)
    ax_xy.set_ylim(0, grid.map_size)
    ax_xy.set_aspect("equal")
    ax_xy.set_title("XY视图 (俯视)", fontsize=12)
    ax_xy.set_xlabel("X")
    ax_xy.set_ylabel("Y")
    ax_xy.legend()
    ax_xy.grid(True, alpha=0.3)

    # XZ视图 (侧视图)
    ax_xz = axes[1]
    # 绘制障碍物投影
    for x in range(grid.map_size):
        for z in range(grid.map_height):
            has_obstacle = any((x, y, z) in grid.obstacles for y in range(grid.map_size))
            if has_obstacle:
                ax_xz.add_patch(plt.Rectangle((x, z), 1, 1, facecolor="red", alpha=0.6))  # type: ignore

    # 绘制路径
    if coarse_path:
        coarse_points = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append((center[0], center[2]))
        if len(coarse_points) > 1:
            xs, zs = zip(*coarse_points)
            ax_xz.plot(xs, zs, "orange", linewidth=4, alpha=0.8, label="粗路径")
            ax_xz.scatter(xs, zs, c="orange", s=60, marker="o", alpha=0.9)

    if refined_path and len(refined_path.points) > 1:
        xs = [p.x for p in refined_path.points]
        zs = [p.z for p in refined_path.points]
        ax_xz.plot(xs, zs, "blue", linewidth=2, alpha=0.9, label="细化路径")

    ax_xz.scatter(
        [start_point.x], [start_point.z], c="green", s=200, marker="s", label="起点"
    )
    ax_xz.scatter(
        [end_point.x], [end_point.z], c="red", s=200, marker="^", label="终点"
    )

    ax_xz.set_xlim(0, grid.map_size)
    ax_xz.set_ylim(0, grid.map_height)
    ax_xz.set_title("XZ视图 (侧视)", fontsize=12)
    ax_xz.set_xlabel("X")
    ax_xz.set_ylabel("Z")
    ax_xz.legend()
    ax_xz.grid(True, alpha=0.3)

    # YZ视图 (正视图)
    ax_yz = axes[2]
    # 绘制障碍物投影
    for y in range(grid.map_size):
        for z in range(grid.map_height):
            has_obstacle = any((x, y, z) in grid.obstacles for x in range(grid.map_size))
            if has_obstacle:
                ax_yz.add_patch(plt.Rectangle((y, z), 1, 1, facecolor="red", alpha=0.6))  # type: ignore

    # 绘制路径
    if coarse_path:
        coarse_points = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append((center[1], center[2]))
        if len(coarse_points) > 1:
            ys, zs = zip(*coarse_points)
            ax_yz.plot(ys, zs, "orange", linewidth=4, alpha=0.8, label="粗路径")
            ax_yz.scatter(ys, zs, c="orange", s=60, marker="o", alpha=0.9)

    if refined_path and len(refined_path.points) > 1:
        ys = [p.y for p in refined_path.points]
        zs = [p.z for p in refined_path.points]
        ax_yz.plot(ys, zs, "blue", linewidth=2, alpha=0.9, label="细化路径")

    ax_yz.scatter(
        [start_point.y], [start_point.z], c="green", s=200, marker="s", label="起点"
    )
    ax_yz.scatter(
        [end_point.y], [end_point.z], c="red", s=200, marker="^", label="终点"
    )

    ax_yz.set_xlim(0, grid.map_size)
    ax_yz.set_ylim(0, grid.map_height)
    ax_yz.set_title("YZ视图 (正视)", fontsize=12)
    ax_yz.set_xlabel("Y")
    ax_yz.set_ylabel("Z")
    ax_yz.legend()
    ax_yz.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig("path_views.png", dpi=150, bbox_inches="tight")  # type: ignore
    print("路径线路图保存为 path_views.png")
    return fig


def visualize_3d_interactive(
    grid: MultiResolutionGrid3D,
    coarse_path: List[int],
    refined_path: Optional[RefinedPath],
    start_point: Point3D,
    end_point: Point3D,
):
    """创建3D交互式可视化"""
    fig = plt.figure(figsize=(15, 10))  # type: ignore
    ax = fig.add_subplot(111, projection="3d")  # type: ignore

    # 绘制路径经过的粗网格边界（半透明）
    print("绘制路径经过的粗网格边界...")
    for cell_id in coarse_path:
        if cell_id < len(grid.cells):
            cell = grid.cells[cell_id]
            bounds = cell.bounds

            # 创建立方体的8个顶点
            vertices = [
                [bounds.x, bounds.y, bounds.z],
                [bounds.x + bounds.width, bounds.y, bounds.z],
                [bounds.x + bounds.width, bounds.y + bounds.height, bounds.z],
                [bounds.x, bounds.y + bounds.height, bounds.z],  # 底面
                [bounds.x, bounds.y, bounds.z + bounds.depth],
                [bounds.x + bounds.width, bounds.y, bounds.z + bounds.depth],
                [
                    bounds.x + bounds.width,
                    bounds.y + bounds.height,
                    bounds.z + bounds.depth,
                ],
                [bounds.x, bounds.y + bounds.height, bounds.z + bounds.depth],  # 顶面
            ]

            # 定义立方体的12条边
            edges = [
                [0, 1],
                [1, 2],
                [2, 3],
                [3, 0],  # 底面
                [4, 5],
                [5, 6],
                [6, 7],
                [7, 4],  # 顶面
                [0, 4],
                [1, 5],
                [2, 6],
                [3, 7],  # 竖直边
            ]

            # 绘制边框
            for edge in edges:
                points = np.array([vertices[edge[0]], vertices[edge[1]]])
                ax.plot3D(  # type: ignore
                    points[:, 0],
                    points[:, 1],
                    points[:, 2],
                    color="lightgray",
                    alpha=0.3,
                    linewidth=0.5,
                )

    # 绘制粗路径
    if coarse_path:
        coarse_points: List[Tuple[float, float, float]] = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append(center)

        if len(coarse_points) > 1:
            xs, ys, zs = zip(*coarse_points)
            ax.plot(  # type: ignore
                xs,
                ys,
                zs,
                color="orange",
                linewidth=4,
                alpha=0.8,
                label="粗路径",
                marker="o",
                markersize=6,
            )

    # 绘制细化路径
    if refined_path and len(refined_path.points) > 1:
        xs = [p.x for p in refined_path.points]
        ys = [p.y for p in refined_path.points]
        zs = [p.z for p in refined_path.points]
        ax.plot(  # type: ignore
            xs,
            ys,
            zs,
            color="blue",
            linewidth=2,
            alpha=0.9,
            label="细化路径",
            marker="s",
            markersize=3,
        )

    # 标注起点和终点
    ax.scatter(  # type: ignore
        [start_point.x],
        [start_point.y],
        [start_point.z],
        c="green",
        s=200,  # type: ignore
        marker="s",
        alpha=1.0,
        edgecolors="darkgreen",
        linewidth=3,
        label="起点",
    )

    ax.scatter(  # type: ignore
        [end_point.x],
        [end_point.y],
        [end_point.z],
        c="red",
        s=200,  # type: ignore
        marker="^",
        alpha=1.0,
        edgecolors="darkred",
        linewidth=3,
        label="终点",
    )

    # 设置坐标轴
    ax.set_xlabel("X", fontsize=12)
    ax.set_ylabel("Y", fontsize=12)
    ax.set_zlabel("Z", fontsize=12)  # type: ignore
    ax.set_title(
        "Python实现的3D自适应路径规划\n（鼠标拖拽旋转，滚轮缩放）", fontsize=14
    )

    # 设置坐标轴范围
    ax.set_xlim(0, grid.map_size)
    ax.set_ylim(0, grid.map_size)
    ax.set_zlim(0, grid.map_height)  # type: ignore

    # 添加图例
    ax.legend(loc="upper right", fontsize=10)

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 设置初始视角
    ax.view_init(elev=20, azim=45)  # type: ignore

    plt.tight_layout()
    return fig


def main():
    """主函数"""
    print("=== Python实现的自适应路径规划算法 (512x512x64 大地图测试) ===")
    import numpy as np

    # 创建网格 - 扩大地图尺寸
    map_size = 512
    map_height = 64
    grid = MultiResolutionGrid3D(map_size, map_height)

    # 添加障碍物 - 与Rust版本完全一致
    print("添加3D障碍物...")

    # 1. 长方体障碍物（建筑物）
    box_obstacles = [
        (20.0, 15.0, 0.0, 8.0, 6.0, 20.0),  # 大建筑
        (45.0, 10.0, 0.0, 4.0, 4.0, 25.0),  # 高楼
        (80.0, 8.0, 0.0, 6.0, 8.0, 15.0),  # 中等建筑
        (110.0, 20.0, 0.0, 5.0, 5.0, 18.0),  # 小建筑
        (15.0, 40.0, 0.0, 3.0, 10.0, 30.0),  # 狭长高楼
        (35.0, 35.0, 0.0, 8.0, 8.0, 12.0),  # 方形建筑
        (60.0, 30.0, 0.0, 4.0, 6.0, 28.0),  # 超高楼
        (85.0, 40.0, 0.0, 6.0, 4.0, 10.0),  # 低矮建筑
    ]

    for i, (x, y, z, w, h, d) in enumerate(box_obstacles):
        grid.add_box_obstacle(x, y, z, w, h, d)
        print(f"  长方体障碍物{i + 1}: x={x}, y={y}, z={z}, 尺寸={w}x{h}x{d}")

    # 2. 圆柱体障碍物（塔楼）
    cylinder_obstacles = [
        (30.0, 80.0, 0.0, 8.0, 25.0),  # 大塔楼
        (90.0, 15.0, 0.0, 3.0, 32.0),  # 细高塔
        (70.0, 100.0, 0.0, 5.0, 20.0),  # 中等塔楼
        (25.0, 70.0, 0.0, 4.0, 22.0),  # 小塔楼
        (105.0, 45.0, 0.0, 6.0, 18.0),  # 粗塔楼
    ]

    for i, (cx, cy, cz, radius, height) in enumerate(cylinder_obstacles):
        grid.add_cylinder_obstacle(cx, cy, cz, radius, height)
        print(
            f"  圆柱体障碍物{i + 1}: 中心({cx}, {cy}, {cz}), 半径={radius}, 高度={height}"
        )

    # 3. 棱柱障碍物（不规则建筑）
    prism_obstacles = [
        # 三角形棱柱
        ([(100.0, 25.0), (110.0, 35.0), (90.0, 35.0)], 0.0, 24.0),
        # 五边形棱柱
        (
            [(50.0, 65.0), (58.0, 65.0), (60.0, 70.0), (55.0, 75.0), (48.0, 70.0)],
            0.0,
            16.0,
        ),
        # L形棱柱
        (
            [
                (75.0, 70.0),
                (85.0, 70.0),
                (85.0, 75.0),
                (80.0, 75.0),
                (80.0, 85.0),
                (75.0, 85.0),
            ],
            0.0,
            20.0,
        ),
    ]

    for i, (vertices, bottom, height) in enumerate(prism_obstacles):
        grid.add_prism_obstacle(vertices, bottom, height)
        print(
            f"  棱柱障碍物{i + 1}: {len(vertices)}个顶点, 底部高度={bottom}, 高度={height}"
        )

    # 4. 3D点集障碍物（山体、岩石群）
    pointset_obstacles = [
        # 山体群1
        (
            [
                (65.0, 15.0, 8.0),
                (68.0, 18.0, 12.0),
                (62.0, 20.0, 6.0),
                (70.0, 22.0, 15.0),
            ],
            3.0,
        ),
        # 岩石群2
        (
            [
                (115.0, 85.0, 5.0),
                (118.0, 87.0, 8.0),
                (112.0, 90.0, 4.0),
                (120.0, 88.0, 10.0),
                (116.0, 92.0, 7.0),
            ],
            2.5,
        ),
        # 小山丘3
        ([(40.0, 95.0, 3.0), (42.0, 98.0, 6.0), (38.0, 100.0, 2.0)], 4.0),
    ]

    for i, (points, radius) in enumerate(pointset_obstacles):
        grid.add_pointset_obstacle(points, radius)
        print(f"  3D点集障碍物{i + 1}: {len(points)}个点，每个半径{radius}")

    # 为512x512x64地图添加更多随机障碍物
    print("\n为更大的地图添加随机障碍物...")
    num_random_boxes = 150
    for _ in range(num_random_boxes):
        x = np.random.uniform(0, map_size - 50)
        y = np.random.uniform(0, map_size - 50)
        z = np.random.uniform(0, map_height - 30)
        w = np.random.uniform(10, 40)
        h = np.random.uniform(10, 40)
        d = np.random.uniform(10, map_height - z - 5)
        grid.add_box_obstacle(x, y, z, w, h, d)
    print(f"  添加了 {num_random_boxes} 个随机长方体障碍物")

    num_random_cylinders = 80
    for _ in range(num_random_cylinders):
        cx = np.random.uniform(10, map_size - 10)
        cy = np.random.uniform(10, map_size - 10)
        cz = 0
        radius = np.random.uniform(5, 25)
        height = np.random.uniform(15, map_height - 5)
        grid.add_cylinder_obstacle(cx, cy, cz, radius, height)
    print(f"  添加了 {num_random_cylinders} 个随机圆柱体障碍物")

    # 构建3D自适应网格
    start_time = time.time()
    grid.build_adaptive_grid()
    grid_time = time.time() - start_time
    print(f"3D网格构建耗时: {grid_time:.3f}s")

    # 构建邻接关系
    start_time = time.time()
    grid.build_adjacency()
    adjacency_time = time.time() - start_time
    print(f"邻接关系构建耗时: {adjacency_time:.3f}s")

    # 路径规划 - 更新起点和终点适应新地图尺寸
    start_point = Point3D(20.0, 480.0, 10.0)
    end_point = Point3D(480.0, 50.0, 30.0)

    print("\n开始路径规划...")
    start_time = time.time()
    coarse_path = grid.find_path_astar(start_point, end_point)
    pathfinding_time = time.time() - start_time
    print(f"粗路径规划耗时: {pathfinding_time:.3f}s")

    if coarse_path:
        print(f"找到粗路径，包含 {len(coarse_path)} 个网格单元")

        # 创建管道和门
        start_time = time.time()
        corridor_id = grid.create_corridor_and_gates(coarse_path)
        corridor_time = time.time() - start_time
        print(f"管道创建耗时: {corridor_time:.3f}s")

        if corridor_id is not None:
            # 门优化路径细化
            print("\n应用门优化路径细化...")
            start_time = time.time()
            refined_path = grid.refine_path_with_gates(
                corridor_id, start_point, end_point
            )
            refine_time = time.time() - start_time
            print(f"路径细化耗时: {refine_time:.3f}s")

            if refined_path:
                print(f"细化路径点数: {len(refined_path.points)}")
                print(f"细化路径长度: {refined_path.total_length:.2f}")
                print(f"路径平滑度: {refined_path.smoothness:.3f}")

            # 生成三个可视化图
            print("\n创建可视化图...")

            # 1. 网格细分切片图
            grid_fig = create_grid_subdivision_slices(grid)

            # 2. 路径线路图
            path_fig = create_path_views(
                grid, coarse_path, refined_path, start_point, end_point
            )

            # 3. 3D交互式可视化
            interactive_fig = visualize_3d_interactive(
                grid, coarse_path, refined_path, start_point, end_point
            )

            # 显示所有图形
            plt.show()

            print("\n可视化完成！")
            print("- 网格细分切片图保存为 grid_subdivision_slices.png")
            print("- 路径线路图保存为 path_views.png")
            print("- 3D交互式可视化已显示（可以鼠标拖拽旋转和缩放）")
        else:
            print("管道创建失败")
    else:
        print("未找到路径")

    print("\n=== 算法执行完成 ===")


if __name__ == "__main__":
    main()
