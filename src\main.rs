use serde::{Serialize, Deserialize};

mod triangular_grid;
use triangular_grid::*;

// 重用现有的基础结构
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct Point {
    pub x: f32,
    pub y: f32,
}

impl Point {
    pub fn new(x: f32, y: f32) -> Self {
        Self { x, y }
    }
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct Point3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Point3D {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Box3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub width: f32,
    pub height: f32,
    pub depth: f32,
}

#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub enum NodeType {
    Empty,
    Blocked,
    Mixed,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum Obstacle3D {
    Box(Box3D),
    Cylinder {
        center: Point3D,
        radius: f32,
        height: f32,
    },
    Prism {
        vertices: Vec<Point>,
        bottom: f32,
        height: f32,
    },
    PointSet {
        points: Vec<Point3D>,
        radius: f32,
    },
}

impl Obstacle3D {
    pub fn contains_point(&self, x: f32, y: f32, z: f32) -> bool {
        match self {
            Obstacle3D::Box(box3d) => {
                x >= box3d.x && x <= box3d.x + box3d.width &&
                y >= box3d.y && y <= box3d.y + box3d.height &&
                z >= box3d.z && z <= box3d.z + box3d.depth
            }
            Obstacle3D::Cylinder { center, radius, height } => {
                let dx = x - center.x;
                let dy = y - center.y;
                let distance_sq = dx * dx + dy * dy;
                distance_sq <= radius * radius &&
                z >= center.z && z <= center.z + height
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                if z < *bottom || z > bottom + height {
                    return false;
                }
                // 简化的点在多边形内检测
                self.point_in_polygon(x, y, vertices)
            }
            Obstacle3D::PointSet { points, radius } => {
                for point in points {
                    let dx = x - point.x;
                    let dy = y - point.y;
                    let dz = z - point.z;
                    let distance_sq = dx * dx + dy * dy + dz * dz;
                    if distance_sq <= radius * radius {
                        return true;
                    }
                }
                false
            }
        }
    }

    fn point_in_polygon(&self, x: f32, y: f32, vertices: &[Point]) -> bool {
        if vertices.len() < 3 {
            return false;
        }

        let mut inside = false;
        let mut j = vertices.len() - 1;

        for i in 0..vertices.len() {
            let xi = vertices[i].x;
            let yi = vertices[i].y;
            let xj = vertices[j].x;
            let yj = vertices[j].y;

            if ((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi) {
                inside = !inside;
            }
            j = i;
        }

        inside
    }
}

fn main() {
    println!("开始创建128x128x32三维德劳内三角剖分测试...");

    // 创建128x128x32的3D德劳内三角网格系统
    let mut triangular_grid = LayeredTriangularGrid::new(128, 32);

    // 添加测试障碍物
    println!("添加测试障碍物...");

    // 添加几个长方体障碍物
    triangular_grid.add_obstacle(Obstacle3D::Box(Box3D {
        x: 20.0, y: 20.0, z: 5.0,
        width: 15.0, height: 10.0, depth: 8.0,
    }));

    triangular_grid.add_obstacle(Obstacle3D::Box(Box3D {
        x: 60.0, y: 40.0, z: 10.0,
        width: 20.0, height: 15.0, depth: 12.0,
    }));

    // 添加圆柱体障碍物
    triangular_grid.add_obstacle(Obstacle3D::Cylinder {
        center: Point3D::new(90.0, 70.0, 8.0),
        radius: 8.0,
        height: 15.0,
    });

    // 添加多边形柱体障碍物
    triangular_grid.add_obstacle(Obstacle3D::Prism {
        vertices: vec![
            Point::new(100.0, 20.0),
            Point::new(110.0, 25.0),
            Point::new(115.0, 35.0),
            Point::new(105.0, 40.0),
            Point::new(95.0, 30.0),
        ],
        bottom: 2.0,
        height: 18.0,
    });

    // 添加点集障碍物
    triangular_grid.add_obstacle(Obstacle3D::PointSet {
        points: vec![
            Point3D::new(40.0, 80.0, 12.0),
            Point3D::new(45.0, 85.0, 14.0),
            Point3D::new(50.0, 90.0, 16.0),
        ],
        radius: 3.0,
    });

    println!("障碍物添加完成，共 {} 个", triangular_grid.obstacles.len());

    // 构建分层三角网格
    triangular_grid.build_layered_triangular_grid();

    // 获取统计信息
    let stats = triangular_grid.get_statistics();
    println!("\n=== 德劳内三角网格统计信息 ===");
    println!("总三角形数量: {}", stats.total_triangles);
    println!("激活三角形数量: {}", stats.active_triangles);
    println!("空旷三角形数量: {}", stats.empty_triangles);
    println!("被占据三角形数量: {}", stats.blocked_triangles);
    println!("总层数: {}", stats.total_layers);
    println!("邻接边总数: {}", stats.total_adjacency_edges);
    println!("LOD分布: {:?}", stats.lod_distribution);

    // 测试路径规划
    println!("\n=== 测试路径规划 ===");
    let start_point = Point3D::new(10.0, 10.0, 5.0);
    let goal_point = Point3D::new(110.0, 110.0, 20.0);

    println!("起点: ({:.1}, {:.1}, {:.1})", start_point.x, start_point.y, start_point.z);
    println!("终点: ({:.1}, {:.1}, {:.1})", goal_point.x, goal_point.y, goal_point.z);

    if let Some(path) = triangular_grid.find_path(start_point, goal_point) {
        println!("找到路径！路径长度: {} 个三角形", path.len());
        println!("路径三角形ID: {:?}", path);
    } else {
        println!("未找到路径");
    }

    // 测试动态LOD
    println!("\n=== 测试动态LOD ===");
    let focus_point = Point3D::new(64.0, 64.0, 16.0);
    triangular_grid.update_lod(focus_point, 50.0);

    let updated_stats = triangular_grid.get_statistics();
    println!("LOD更新后激活三角形数量: {}", updated_stats.active_triangles);
    println!("LOD分布: {:?}", updated_stats.lod_distribution);

    // 导出可视化数据
    println!("\n=== 导出可视化数据 ===");
    let vis_data = triangular_grid.export_visualization_data();

    match serde_json::to_string_pretty(&vis_data) {
        Ok(json_data) => {
            if let Err(e) = std::fs::write("triangular_grid_data.json", json_data) {
                println!("写入文件失败: {}", e);
            } else {
                println!("可视化数据已导出到 triangular_grid_data.json");
            }
        }
        Err(e) => {
            println!("序列化失败: {}", e);
        }
    }

    println!("\n=== 德劳内三角剖分测试完成 ===");
    println!("与传统立方体网格相比的优势:");
    println!("  - 三角形数量显著减少（约为立方体网格的1/10）");
    println!("  - 更好地适应障碍物边界");
    println!("  - 支持动态LOD优化");
    println!("  - 快速空间索引查询");
    println!("  - 更自然的路径规划结果");
}