use std::collections::{HashMap, HashSet, BinaryHeap};
use std::cmp::Ordering;
use serde::{Serialize, Deserialize};

// 重用现有的基础结构
use crate::{Point, Point3D, Box3D, Obstacle3D, NodeType};

/// 三角形网格单元
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriangleCell {
    pub id: usize,
    pub vertices: [Point; 3],      // 三角形的三个顶点
    pub z_range: (f32, f32),       // Z轴范围
    pub node_type: NodeType,       // 障碍物类型
    pub layer: u32,                // 所属层级
    pub lod_level: u8,             // LOD级别
    pub is_active: bool,           // 是否当前激活
    pub area: f32,                 // 三角形面积
}

/// 分层三角网格系统
#[derive(Debug)]
pub struct LayeredTriangularGrid {
    pub layers: Vec<TriangularLayer>,
    pub map_size: u32,
    pub map_height: u32,
    pub layer_thickness: f32,
    pub obstacles: Vec<Obstacle3D>,
    pub adjacency: HashMap<usize, Vec<(usize, f32)>>,
    pub spatial_indices: Vec<GridHashIndex>,  // 每层的空间索引
    pub lod_manager: DynamicLODManager,
}

/// 单层三角网格
#[derive(Debug)]
pub struct TriangularLayer {
    pub z_min: f32,
    pub z_max: f32,
    pub triangles: Vec<TriangleCell>,
    pub obstacle_points: Vec<Point>,
    pub boundary_points: Vec<Point>,
    pub delaunay_edges: Vec<(usize, usize)>,  // 德劳内三角剖分的边
}

/// 网格哈希索引（用于快速点查询）
#[derive(Debug)]
pub struct GridHashIndex {
    pub cell_size: f32,
    pub grid_width: u32,
    pub grid_height: u32,
    pub hash_table: HashMap<(u32, u32), Vec<usize>>, // (grid_x, grid_y) -> triangle_ids
}

/// 动态LOD管理器
#[derive(Debug)]
pub struct DynamicLODManager {
    pub lod_levels: Vec<LODLevel>,
    pub current_focus_point: Option<Point3D>,
    pub view_distance: f32,
}

/// LOD级别定义
#[derive(Debug, Clone)]
pub struct LODLevel {
    pub level: u8,
    pub max_triangle_size: f32,
    pub distance_threshold: f32,
    pub triangle_density: f32,
}

/// 垂直连接信息
#[derive(Debug, Clone)]
pub struct VerticalConnection {
    pub lower_triangle_id: usize,
    pub upper_triangle_id: usize,
    pub connection_area: f32,
    pub vertical_clearance: f32,
    pub connection_type: VerticalConnectionType,
}

#[derive(Debug, Clone)]
pub enum VerticalConnectionType {
    FullyOpen,
    PartiallyOpen,
    Restricted,
}

/// 路径规划节点
#[derive(Debug, Clone)]
pub struct TriangularPathNode {
    pub triangle_id: usize,
    pub g_cost: f32,
    pub h_cost: f32,
    pub parent: Option<usize>,
}

impl TriangularPathNode {
    pub fn f_cost(&self) -> f32 {
        self.g_cost + self.h_cost
    }
}

impl Eq for TriangularPathNode {}

impl PartialEq for TriangularPathNode {
    fn eq(&self, other: &Self) -> bool {
        self.triangle_id == other.triangle_id
    }
}

impl Ord for TriangularPathNode {
    fn cmp(&self, other: &Self) -> Ordering {
        other.f_cost().partial_cmp(&self.f_cost()).unwrap_or(Ordering::Equal)
    }
}

impl PartialOrd for TriangularPathNode {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl TriangleCell {
    /// 计算三角形面积
    pub fn calculate_area(vertices: &[Point; 3]) -> f32 {
        let a = &vertices[0];
        let b = &vertices[1];
        let c = &vertices[2];
        
        // 使用叉积公式计算面积
        let ab_x = b.x - a.x;
        let ab_y = b.y - a.y;
        let ac_x = c.x - a.x;
        let ac_y = c.y - a.y;
        
        0.5 * (ab_x * ac_y - ab_y * ac_x).abs()
    }
    
    /// 获取三角形中心点
    pub fn center(&self) -> Point {
        Point::new(
            (self.vertices[0].x + self.vertices[1].x + self.vertices[2].x) / 3.0,
            (self.vertices[0].y + self.vertices[1].y + self.vertices[2].y) / 3.0,
        )
    }
    
    /// 检查点是否在三角形内
    pub fn contains_point(&self, point: &Point) -> bool {
        Self::point_in_triangle(point, &self.vertices)
    }
    
    /// 点在三角形内检测（重心坐标法）
    pub fn point_in_triangle(point: &Point, vertices: &[Point; 3]) -> bool {
        let a = &vertices[0];
        let b = &vertices[1];
        let c = &vertices[2];
        
        // 计算重心坐标
        let denom = (b.y - c.y) * (a.x - c.x) + (c.x - b.x) * (a.y - c.y);
        if denom.abs() < 1e-10 {
            return false; // 退化三角形
        }
        
        let alpha = ((b.y - c.y) * (point.x - c.x) + (c.x - b.x) * (point.y - c.y)) / denom;
        let beta = ((c.y - a.y) * (point.x - c.x) + (a.x - c.x) * (point.y - c.y)) / denom;
        let gamma = 1.0 - alpha - beta;
        
        alpha >= 0.0 && beta >= 0.0 && gamma >= 0.0
    }
}

impl GridHashIndex {
    pub fn new(map_size: f32, cell_size: f32) -> Self {
        let grid_width = (map_size / cell_size).ceil() as u32;
        let grid_height = grid_width;
        
        Self {
            cell_size,
            grid_width,
            grid_height,
            hash_table: HashMap::new(),
        }
    }
    
    /// 将三角形添加到空间索引
    pub fn add_triangle(&mut self, triangle_id: usize, triangle: &TriangleCell) {
        // 计算三角形的包围盒
        let min_x = triangle.vertices.iter().map(|p| p.x).fold(f32::INFINITY, f32::min);
        let max_x = triangle.vertices.iter().map(|p| p.x).fold(f32::NEG_INFINITY, f32::max);
        let min_y = triangle.vertices.iter().map(|p| p.y).fold(f32::INFINITY, f32::min);
        let max_y = triangle.vertices.iter().map(|p| p.y).fold(f32::NEG_INFINITY, f32::max);
        
        // 计算覆盖的网格范围
        let start_x = (min_x / self.cell_size).floor() as u32;
        let end_x = (max_x / self.cell_size).ceil() as u32;
        let start_y = (min_y / self.cell_size).floor() as u32;
        let end_y = (max_y / self.cell_size).ceil() as u32;
        
        // 将三角形添加到所有相关的网格单元
        for gx in start_x..=end_x.min(self.grid_width - 1) {
            for gy in start_y..=end_y.min(self.grid_height - 1) {
                self.hash_table.entry((gx, gy)).or_insert_with(Vec::new).push(triangle_id);
            }
        }
    }
    
    /// 查询包含指定点的三角形候选集
    pub fn query_point(&self, x: f32, y: f32) -> Vec<usize> {
        let grid_x = (x / self.cell_size) as u32;
        let grid_y = (y / self.cell_size) as u32;
        
        if let Some(triangle_ids) = self.hash_table.get(&(grid_x, grid_y)) {
            triangle_ids.clone()
        } else {
            Vec::new()
        }
    }
}

impl DynamicLODManager {
    pub fn new() -> Self {
        let lod_levels = vec![
            LODLevel { level: 0, max_triangle_size: 2.0, distance_threshold: 10.0, triangle_density: 1.0 },
            LODLevel { level: 1, max_triangle_size: 5.0, distance_threshold: 25.0, triangle_density: 0.7 },
            LODLevel { level: 2, max_triangle_size: 10.0, distance_threshold: 50.0, triangle_density: 0.4 },
            LODLevel { level: 3, max_triangle_size: 20.0, distance_threshold: f32::INFINITY, triangle_density: 0.2 },
        ];

        Self {
            lod_levels,
            current_focus_point: None,
            view_distance: 50.0,
        }
    }

    /// 计算所需的LOD级别
    pub fn calculate_required_lod(&self, distance: f32) -> u8 {
        Self::calculate_required_lod_static(distance)
    }

    /// 计算所需的LOD级别（静态方法）
    pub fn calculate_required_lod_static(distance: f32) -> u8 {
        let lod_levels = vec![
            LODLevel { level: 0, max_triangle_size: 2.0, distance_threshold: 10.0, triangle_density: 1.0 },
            LODLevel { level: 1, max_triangle_size: 5.0, distance_threshold: 25.0, triangle_density: 0.7 },
            LODLevel { level: 2, max_triangle_size: 10.0, distance_threshold: 50.0, triangle_density: 0.4 },
            LODLevel { level: 3, max_triangle_size: 20.0, distance_threshold: f32::INFINITY, triangle_density: 0.2 },
        ];

        for lod in &lod_levels {
            if distance <= lod.distance_threshold {
                return lod.level;
            }
        }
        3 // 最低精度
    }
}

impl LayeredTriangularGrid {
    pub fn new(map_size: u32, map_height: u32) -> Self {
        Self {
            layers: Vec::new(),
            map_size,
            map_height,
            layer_thickness: 5.0, // 默认层厚度
            obstacles: Vec::new(),
            adjacency: HashMap::new(),
            spatial_indices: Vec::new(),
            lod_manager: DynamicLODManager::new(),
        }
    }

    /// 添加3D障碍物
    pub fn add_obstacle(&mut self, obstacle: Obstacle3D) {
        self.obstacles.push(obstacle);
    }

    /// 构建分层三角网格
    pub fn build_layered_triangular_grid(&mut self) {
        println!("开始构建分层德劳内三角网格...");
        let start_time = std::time::Instant::now();

        self.layers.clear();
        self.spatial_indices.clear();

        // 计算层数
        let num_layers = (self.map_height as f32 / self.layer_thickness).ceil() as u32;
        println!("将构建 {} 层，每层厚度 {}", num_layers, self.layer_thickness);

        // 为每层构建三角网格
        for layer_idx in 0..num_layers {
            let z_min = layer_idx as f32 * self.layer_thickness;
            let z_max = ((layer_idx + 1) as f32 * self.layer_thickness).min(self.map_height as f32);

            println!("构建第 {} 层 (Z: {:.1} - {:.1})", layer_idx + 1, z_min, z_max);

            let layer = self.build_triangular_layer(z_min, z_max, layer_idx);

            // 构建该层的空间索引
            let mut spatial_index = GridHashIndex::new(self.map_size as f32, 5.0);
            for (triangle_id, triangle) in layer.triangles.iter().enumerate() {
                spatial_index.add_triangle(triangle_id, triangle);
            }

            self.layers.push(layer);
            self.spatial_indices.push(spatial_index);
        }

        // 构建邻接关系
        self.build_triangular_adjacency();

        let duration = start_time.elapsed();
        println!("分层德劳内三角网格构建完成，耗时: {:?}", duration);

        // 统计信息
        let total_triangles: usize = self.layers.iter().map(|layer| layer.triangles.len()).sum();
        println!("总三角形数量: {}", total_triangles);
        println!("平均每层三角形数量: {:.1}", total_triangles as f32 / num_layers as f32);
    }

    /// 为单层构建德劳内三角剖分
    fn build_triangular_layer(&self, z_min: f32, z_max: f32, layer_idx: u32) -> TriangularLayer {
        // 1. 收集该层的约束点
        let mut constraint_points = Vec::new();

        // 添加地图边界点
        constraint_points.extend(self.generate_boundary_points());

        // 添加障碍物边界点
        for obstacle in &self.obstacles {
            if self.obstacle_intersects_layer(obstacle, z_min, z_max) {
                constraint_points.extend(self.extract_obstacle_boundary_points(obstacle, z_min, z_max));
            }
        }

        // 2. 执行德劳内三角剖分（简化版本）
        let triangulation = self.simple_delaunay_triangulation(&constraint_points);

        // 3. 分类三角形
        let triangles = self.classify_triangles(triangulation, z_min, z_max, layer_idx);

        TriangularLayer {
            z_min,
            z_max,
            triangles,
            obstacle_points: constraint_points.clone(),
            boundary_points: self.generate_boundary_points(),
            delaunay_edges: Vec::new(), // 暂时为空
        }
    }

    /// 生成地图边界点
    fn generate_boundary_points(&self) -> Vec<Point> {
        let size = self.map_size as f32;
        let step = 10.0; // 边界点间距
        let mut points = Vec::new();

        // 四条边界
        let mut x = 0.0;
        while x <= size {
            points.push(Point::new(x, 0.0));      // 下边界
            points.push(Point::new(x, size));     // 上边界
            x += step;
        }

        let mut y = step; // 避免重复角点
        while y < size {
            points.push(Point::new(0.0, y));      // 左边界
            points.push(Point::new(size, y));     // 右边界
            y += step;
        }

        points
    }

    /// 检查障碍物是否与指定层相交
    fn obstacle_intersects_layer(&self, obstacle: &Obstacle3D, z_min: f32, z_max: f32) -> bool {
        match obstacle {
            Obstacle3D::Box(box3d) => {
                !(box3d.z + box3d.depth <= z_min || box3d.z >= z_max)
            }
            Obstacle3D::Cylinder { center, height, .. } => {
                !(center.z + height <= z_min || center.z >= z_max)
            }
            Obstacle3D::Prism { bottom, height, .. } => {
                !(bottom + height <= z_min || *bottom >= z_max)
            }
            Obstacle3D::PointSet { points, .. } => {
                points.iter().any(|p| p.z >= z_min && p.z <= z_max)
            }
        }
    }

    /// 提取障碍物在指定层的边界点
    fn extract_obstacle_boundary_points(&self, obstacle: &Obstacle3D, _z_min: f32, _z_max: f32) -> Vec<Point> {
        match obstacle {
            Obstacle3D::Box(box3d) => {
                self.extract_box_boundary_points(box3d)
            }
            Obstacle3D::Cylinder { center, radius, .. } => {
                self.extract_cylinder_boundary_points(center, *radius)
            }
            Obstacle3D::Prism { vertices, .. } => {
                vertices.clone()
            }
            Obstacle3D::PointSet { points, radius } => {
                self.extract_pointset_boundary_points(points, *radius)
            }
        }
    }

    /// 提取长方体的边界点
    fn extract_box_boundary_points(&self, box3d: &Box3D) -> Vec<Point> {
        vec![
            Point::new(box3d.x, box3d.y),
            Point::new(box3d.x + box3d.width, box3d.y),
            Point::new(box3d.x + box3d.width, box3d.y + box3d.height),
            Point::new(box3d.x, box3d.y + box3d.height),
        ]
    }

    /// 提取圆柱体的边界点（圆形采样）
    fn extract_cylinder_boundary_points(&self, center: &Point3D, radius: f32) -> Vec<Point> {
        let num_points = 16; // 圆周采样点数
        let mut points = Vec::new();

        for i in 0..num_points {
            let angle = 2.0 * std::f32::consts::PI * i as f32 / num_points as f32;
            let x = center.x + radius * angle.cos();
            let y = center.y + radius * angle.sin();
            points.push(Point::new(x, y));
        }

        points
    }

    /// 提取点集的边界点（为每个点生成小圆）
    fn extract_pointset_boundary_points(&self, points: &[Point3D], radius: f32) -> Vec<Point> {
        let mut boundary_points = Vec::new();

        for point in points {
            let num_circle_points = 8; // 每个点周围的采样数
            for i in 0..num_circle_points {
                let angle = 2.0 * std::f32::consts::PI * i as f32 / num_circle_points as f32;
                let x = point.x + radius * angle.cos();
                let y = point.y + radius * angle.sin();
                boundary_points.push(Point::new(x, y));
            }
        }

        boundary_points
    }

    /// 简化的德劳内三角剖分（使用网格化方法）
    fn simple_delaunay_triangulation(&self, points: &[Point]) -> Vec<[Point; 3]> {
        if points.len() < 3 {
            return Vec::new();
        }

        // 简化版本：使用规则网格 + 约束点
        let mut triangles = Vec::new();
        let grid_size = 8.0; // 网格大小
        let map_size = self.map_size as f32;

        // 生成规则网格三角形
        let mut y = 0.0;
        while y < map_size {
            let mut x = 0.0;
            while x < map_size {
                // 每个网格单元生成两个三角形
                let p1 = Point::new(x, y);
                let p2 = Point::new(x + grid_size, y);
                let p3 = Point::new(x + grid_size, y + grid_size);
                let p4 = Point::new(x, y + grid_size);

                // 检查三角形是否在地图范围内
                if x + grid_size <= map_size && y + grid_size <= map_size {
                    triangles.push([p1, p2, p3]);
                    triangles.push([p1, p3, p4]);
                }

                x += grid_size;
            }
            y += grid_size;
        }

        // 添加约束点形成的三角形（简化处理）
        // 这里可以实现更复杂的约束德劳内三角剖分算法

        triangles
    }

    /// 分类三角形（空旷/障碍/混合）
    fn classify_triangles(&self, triangulation: Vec<[Point; 3]>, z_min: f32, z_max: f32, layer_idx: u32) -> Vec<TriangleCell> {
        let mut triangles = Vec::new();

        for (id, vertices) in triangulation.into_iter().enumerate() {
            let area = TriangleCell::calculate_area(&vertices);
            if area < 0.1 {
                continue; // 跳过面积太小的三角形
            }

            // 检查三角形中心点是否被障碍物占据
            let center = Point::new(
                (vertices[0].x + vertices[1].x + vertices[2].x) / 3.0,
                (vertices[0].y + vertices[1].y + vertices[2].y) / 3.0,
            );

            let node_type = self.classify_triangle_by_obstacles(&center, z_min, z_max);

            let triangle = TriangleCell {
                id,
                vertices,
                z_range: (z_min, z_max),
                node_type,
                layer: layer_idx,
                lod_level: 0, // 默认最高精度
                is_active: true,
                area,
            };

            triangles.push(triangle);
        }

        triangles
    }

    /// 根据障碍物分类三角形
    fn classify_triangle_by_obstacles(&self, center: &Point, z_min: f32, z_max: f32) -> NodeType {
        let z_center = (z_min + z_max) / 2.0;

        for obstacle in &self.obstacles {
            if obstacle.contains_point(center.x, center.y, z_center) {
                return NodeType::Blocked;
            }
        }

        NodeType::Empty
    }

    /// 构建三角网格的邻接关系
    fn build_triangular_adjacency(&mut self) {
        println!("构建三角网格邻接关系...");
        let start_time = std::time::Instant::now();

        self.adjacency.clear();

        // 为每个三角形分配全局ID
        let mut global_id = 0;
        for layer in &mut self.layers {
            for triangle in &mut layer.triangles {
                triangle.id = global_id;
                self.adjacency.insert(global_id, Vec::new());
                global_id += 1;
            }
        }

        // 构建层内邻接关系
        for layer_idx in 0..self.layers.len() {
            self.build_intra_layer_adjacency_by_index(layer_idx);
        }

        // 构建层间邻接关系
        for layer_idx in 0..(self.layers.len() - 1) {
            self.build_inter_layer_adjacency(layer_idx, layer_idx + 1);
        }

        let duration = start_time.elapsed();
        println!("邻接关系构建完成，耗时: {:?}", duration);

        let total_edges: usize = self.adjacency.values().map(|neighbors| neighbors.len()).sum();
        println!("总边数: {}", total_edges);
    }

    /// 构建层内邻接关系（通过索引）
    fn build_intra_layer_adjacency_by_index(&mut self, layer_idx: usize) {
        // 先收集需要的数据，避免借用冲突
        let mut adjacency_updates = Vec::new();

        {
            let layer = &self.layers[layer_idx];
            for i in 0..layer.triangles.len() {
                for j in (i + 1)..layer.triangles.len() {
                    let triangle_a = &layer.triangles[i];
                    let triangle_b = &layer.triangles[j];

                    // 只连接空旷的三角形
                    if triangle_a.node_type == NodeType::Blocked || triangle_b.node_type == NodeType::Blocked {
                        continue;
                    }

                    // 检查是否共享边
                    if self.triangles_share_edge(triangle_a, triangle_b) {
                        let cost = self.calculate_triangle_distance(triangle_a, triangle_b);
                        adjacency_updates.push((triangle_a.id, triangle_b.id, cost));
                    }
                }
            }
        }

        // 应用邻接关系更新
        for (id_a, id_b, cost) in adjacency_updates {
            self.adjacency.get_mut(&id_a).unwrap().push((id_b, cost));
            self.adjacency.get_mut(&id_b).unwrap().push((id_a, cost));
        }
    }

    /// 构建层间邻接关系
    fn build_inter_layer_adjacency(&mut self, lower_layer_idx: usize, upper_layer_idx: usize) {
        let lower_layer = &self.layers[lower_layer_idx];
        let upper_layer = &self.layers[upper_layer_idx];

        for lower_triangle in &lower_layer.triangles {
            if lower_triangle.node_type == NodeType::Blocked {
                continue;
            }

            for upper_triangle in &upper_layer.triangles {
                if upper_triangle.node_type == NodeType::Blocked {
                    continue;
                }

                // 检查三角形投影是否重叠
                if self.triangles_overlap_vertically(lower_triangle, upper_triangle) {
                    let vertical_cost = self.calculate_vertical_cost(lower_triangle, upper_triangle);

                    self.adjacency.get_mut(&lower_triangle.id).unwrap().push((upper_triangle.id, vertical_cost));
                    self.adjacency.get_mut(&upper_triangle.id).unwrap().push((lower_triangle.id, vertical_cost));
                }
            }
        }
    }

    /// 检查两个三角形是否共享边
    fn triangles_share_edge(&self, triangle_a: &TriangleCell, triangle_b: &TriangleCell) -> bool {
        let mut shared_vertices = 0;

        for vertex_a in &triangle_a.vertices {
            for vertex_b in &triangle_b.vertices {
                if (vertex_a.x - vertex_b.x).abs() < 0.1 && (vertex_a.y - vertex_b.y).abs() < 0.1 {
                    shared_vertices += 1;
                }
            }
        }

        shared_vertices >= 2 // 共享至少两个顶点（即一条边）
    }

    /// 检查两个三角形是否在垂直方向上重叠
    fn triangles_overlap_vertically(&self, lower: &TriangleCell, upper: &TriangleCell) -> bool {
        // 简化检查：比较三角形中心点的距离
        let lower_center = lower.center();
        let upper_center = upper.center();

        let distance = ((lower_center.x - upper_center.x).powi(2) +
                       (lower_center.y - upper_center.y).powi(2)).sqrt();

        // 如果中心点距离小于两个三角形的平均"半径"，则认为重叠
        let avg_radius = (lower.area.sqrt() + upper.area.sqrt()) / 4.0;
        distance < avg_radius
    }

    /// 计算三角形之间的距离
    fn calculate_triangle_distance(&self, triangle_a: &TriangleCell, triangle_b: &TriangleCell) -> f32 {
        let center_a = triangle_a.center();
        let center_b = triangle_b.center();

        ((center_a.x - center_b.x).powi(2) + (center_a.y - center_b.y).powi(2)).sqrt()
    }

    /// 计算垂直移动代价
    fn calculate_vertical_cost(&self, lower: &TriangleCell, upper: &TriangleCell) -> f32 {
        let horizontal_distance = self.calculate_triangle_distance(lower, upper);
        let vertical_distance = upper.z_range.0 - lower.z_range.1;

        // 垂直移动代价更高
        horizontal_distance + vertical_distance * 2.0
    }

    /// 快速查找点所在的三角形
    pub fn find_triangle_containing_point(&self, point: &Point3D) -> Option<usize> {
        // 1. 确定Z层
        let layer_idx = self.get_layer_index(point.z)?;
        let layer = &self.layers[layer_idx];
        let spatial_index = &self.spatial_indices[layer_idx];

        // 2. 在该层的空间索引中查找候选三角形
        let candidate_triangles = spatial_index.query_point(point.x, point.y);

        // 3. 精确检查候选三角形
        for triangle_id in candidate_triangles {
            if triangle_id < layer.triangles.len() {
                let triangle = &layer.triangles[triangle_id];
                let point_2d = Point::new(point.x, point.y);
                if triangle.contains_point(&point_2d) {
                    return Some(triangle.id);
                }
            }
        }

        None
    }

    /// 获取Z坐标对应的层索引
    fn get_layer_index(&self, z: f32) -> Option<usize> {
        if z < 0.0 || z >= self.map_height as f32 {
            return None;
        }
        let layer_idx = (z / self.layer_thickness) as usize;
        if layer_idx < self.layers.len() {
            Some(layer_idx)
        } else {
            None
        }
    }

    /// A*路径规划
    pub fn find_path(&self, start: Point3D, goal: Point3D) -> Option<Vec<usize>> {
        // 找到起点和终点所在的三角形
        let start_triangle_id = self.find_triangle_containing_point(&start)?;
        let goal_triangle_id = self.find_triangle_containing_point(&goal)?;

        if start_triangle_id == goal_triangle_id {
            return Some(vec![start_triangle_id]);
        }

        // A*搜索
        let mut open_set = BinaryHeap::new();
        let mut closed_set = HashSet::new();
        let mut came_from = HashMap::new();

        let start_node = TriangularPathNode {
            triangle_id: start_triangle_id,
            g_cost: 0.0,
            h_cost: self.heuristic_cost(start_triangle_id, goal_triangle_id, &goal),
            parent: None,
        };

        open_set.push(start_node);

        while let Some(current) = open_set.pop() {
            if current.triangle_id == goal_triangle_id {
                // 重构路径
                return Some(self.reconstruct_path(&came_from, current.triangle_id));
            }

            closed_set.insert(current.triangle_id);

            // 检查邻居
            if let Some(neighbors) = self.adjacency.get(&current.triangle_id) {
                for &(neighbor_id, edge_cost) in neighbors {
                    if closed_set.contains(&neighbor_id) {
                        continue;
                    }

                    let tentative_g_cost = current.g_cost + edge_cost;
                    let h_cost = self.heuristic_cost(neighbor_id, goal_triangle_id, &goal);

                    let neighbor_node = TriangularPathNode {
                        triangle_id: neighbor_id,
                        g_cost: tentative_g_cost,
                        h_cost,
                        parent: Some(current.triangle_id),
                    };

                    came_from.insert(neighbor_id, current.triangle_id);
                    open_set.push(neighbor_node);
                }
            }
        }

        None // 未找到路径
    }

    /// 启发式代价函数
    fn heuristic_cost(&self, triangle_id: usize, goal_triangle_id: usize, goal_point: &Point3D) -> f32 {
        // 找到三角形
        let triangle = self.find_triangle_by_id(triangle_id);
        let goal_triangle = self.find_triangle_by_id(goal_triangle_id);

        if let (Some(tri), Some(goal_tri)) = (triangle, goal_triangle) {
            let center = tri.center();
            let goal_center = goal_tri.center();

            // 3D欧几里得距离
            let dx = center.x - goal_center.x;
            let dy = center.y - goal_center.y;
            let dz = (tri.z_range.0 + tri.z_range.1) / 2.0 - goal_point.z;

            (dx * dx + dy * dy + dz * dz).sqrt()
        } else {
            f32::INFINITY
        }
    }

    /// 根据ID查找三角形
    fn find_triangle_by_id(&self, triangle_id: usize) -> Option<&TriangleCell> {
        for layer in &self.layers {
            for triangle in &layer.triangles {
                if triangle.id == triangle_id {
                    return Some(triangle);
                }
            }
        }
        None
    }

    /// 重构路径
    fn reconstruct_path(&self, came_from: &HashMap<usize, usize>, mut current: usize) -> Vec<usize> {
        let mut path = vec![current];

        while let Some(&parent) = came_from.get(&current) {
            path.push(parent);
            current = parent;
        }

        path.reverse();
        path
    }

    /// 动态LOD更新
    pub fn update_lod(&mut self, focus_point: Point3D, view_distance: f32) {
        self.lod_manager.current_focus_point = Some(focus_point);
        self.lod_manager.view_distance = view_distance;

        // 更新每个三角形的LOD级别和激活状态
        for layer in &mut self.layers {
            for triangle in &mut layer.triangles {
                let center = triangle.center();
                let center_3d = Point3D::new(center.x, center.y, (triangle.z_range.0 + triangle.z_range.1) / 2.0);

                let distance = Self::calculate_3d_distance_static(&focus_point, &center_3d);
                let required_lod = DynamicLODManager::calculate_required_lod_static(distance);

                triangle.lod_level = required_lod;
                triangle.is_active = distance <= view_distance;
            }
        }
    }

    /// 计算3D距离
    fn calculate_3d_distance(&self, p1: &Point3D, p2: &Point3D) -> f32 {
        Self::calculate_3d_distance_static(p1, p2)
    }

    /// 计算3D距离（静态方法）
    fn calculate_3d_distance_static(p1: &Point3D, p2: &Point3D) -> f32 {
        let dx = p1.x - p2.x;
        let dy = p1.y - p2.y;
        let dz = p1.z - p2.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// 获取统计信息
    pub fn get_statistics(&self) -> TriangularGridStatistics {
        let mut total_triangles = 0;
        let mut active_triangles = 0;
        let mut blocked_triangles = 0;
        let mut empty_triangles = 0;
        let mut lod_distribution = [0; 4];

        for layer in &self.layers {
            for triangle in &layer.triangles {
                total_triangles += 1;

                if triangle.is_active {
                    active_triangles += 1;
                }

                match triangle.node_type {
                    NodeType::Blocked => blocked_triangles += 1,
                    NodeType::Empty => empty_triangles += 1,
                    _ => {}
                }

                if triangle.lod_level < 4 {
                    lod_distribution[triangle.lod_level as usize] += 1;
                }
            }
        }

        TriangularGridStatistics {
            total_triangles,
            active_triangles,
            blocked_triangles,
            empty_triangles,
            total_layers: self.layers.len(),
            lod_distribution,
            total_adjacency_edges: self.adjacency.values().map(|v| v.len()).sum(),
        }
    }

    /// 导出可视化数据
    pub fn export_visualization_data(&self) -> TriangularVisualizationData {
        let mut triangles_data = Vec::new();
        let mut layer_info = Vec::new();

        for (layer_idx, layer) in self.layers.iter().enumerate() {
            let mut layer_triangles = Vec::new();

            for triangle in &layer.triangles {
                let triangle_data = TriangleVisualizationData {
                    id: triangle.id,
                    vertices: triangle.vertices.clone(),
                    center: triangle.center(),
                    z_range: triangle.z_range,
                    node_type: triangle.node_type.clone(),
                    lod_level: triangle.lod_level,
                    is_active: triangle.is_active,
                    area: triangle.area,
                };

                layer_triangles.push(triangle_data.clone());
                triangles_data.push(triangle_data);
            }

            layer_info.push(LayerVisualizationData {
                layer_index: layer_idx,
                z_min: layer.z_min,
                z_max: layer.z_max,
                triangle_count: layer_triangles.len(),
                triangles: layer_triangles,
            });
        }

        TriangularVisualizationData {
            map_size: self.map_size,
            map_height: self.map_height,
            layer_thickness: self.layer_thickness,
            triangles: triangles_data,
            layers: layer_info,
            obstacles: self.obstacles.clone(),
            statistics: self.get_statistics(),
        }
    }
}

/// 统计信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriangularGridStatistics {
    pub total_triangles: usize,
    pub active_triangles: usize,
    pub blocked_triangles: usize,
    pub empty_triangles: usize,
    pub total_layers: usize,
    pub lod_distribution: [usize; 4],
    pub total_adjacency_edges: usize,
}

/// 可视化数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriangularVisualizationData {
    pub map_size: u32,
    pub map_height: u32,
    pub layer_thickness: f32,
    pub triangles: Vec<TriangleVisualizationData>,
    pub layers: Vec<LayerVisualizationData>,
    pub obstacles: Vec<Obstacle3D>,
    pub statistics: TriangularGridStatistics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriangleVisualizationData {
    pub id: usize,
    pub vertices: [Point; 3],
    pub center: Point,
    pub z_range: (f32, f32),
    pub node_type: NodeType,
    pub lod_level: u8,
    pub is_active: bool,
    pub area: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayerVisualizationData {
    pub layer_index: usize,
    pub z_min: f32,
    pub z_max: f32,
    pub triangle_count: usize,
    pub triangles: Vec<TriangleVisualizationData>,
}
